25-09-08 09:18:03:234 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-08 09:18:03:236 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"d56e72cb-4bd8-41e2-a35f-a1e8267b5deb","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-08 09:18:11:315 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-08 09:18:11:476 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:11:477 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:18:11:621 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:11:622 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:11:626 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:11:628 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:11:632 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:11:633 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-08 09:18:11:669 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:11:670 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:18:11:672 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-08 09:18:11:724 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:18:15:203 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-08 09:18:15:254 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:15:255 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:18:15:298 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:15:300 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-08 09:18:18:781 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:18:782 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-08 09:18:18:788 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:18:789 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-08 09:18:18:791 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-08 09:18:18:840 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:18:841 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:19:056 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:19:057 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:19:556 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:19:557 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:25:034 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-08 09:18:25:084 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:25:085 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:18:25:101 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:25:102 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-08 09:18:25:128 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:25:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:18:25:130 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-08 09:18:25:189 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:18:25:408 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:25:409 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-08 09:18:25:575 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:25:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:26:683 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-08 09:18:26:742 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:26:743 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:18:26:784 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:26:786 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-08 09:18:26:792 DESKTOP-3BSREDP ERROR [clj-scheduler.mq:103] - exception: clojure.lang.ExceptionInfo: 服务器错误
{:type :biz/error, :key 1500, :msg "流程图未开始或已结束", :data nil}
 at clj_backend.common.biz_error$backend_throw.invokeStatic (biz_error.clj:15)
    clj_backend.common.biz_error$backend_throw.invoke (biz_error.clj:11)
    clj_backend.common.biz_error$backend_throw.invokeStatic (biz_error.clj:13)
    clj_backend.common.biz_error$backend_throw.invoke (biz_error.clj:11)
    clj_backend.common.biz_error$throw_error.invokeStatic (biz_error.clj:43)
    clj_backend.common.biz_error$throw_error.invoke (biz_error.clj:32)
    clj_scheduler.context$mgr_abort.invokeStatic (context.clj:1137)
    clj_scheduler.context$mgr_abort.invoke (context.clj:1132)
    clj_backend.modules.action.action_service$eval39658$fn__39659.invoke (action_service.clj:207)
    clojure.lang.MultiFn.invoke (MultiFn.java:229)
    clj_scheduler.mq$start_receive$fn__32004.invoke (mq.clj:97)
    clj_scheduler.mq$start_receive.invokeStatic (mq.clj:92)
    clj_scheduler.mq$start_receive.invoke (mq.clj:89)
    clj_scheduler.mq$start_script_client$fn__32018.invoke (mq.clj:116)
    clojure.core$binding_conveyor_fn$fn__5842.invoke (core.clj:2047)
    clojure.lang.AFn.call (AFn.java:18)
    java.util.concurrent.FutureTask.run (FutureTask.java:264)
    java.util.concurrent.ThreadPoolExecutor.runWorker (ThreadPoolExecutor.java:1136)
    java.util.concurrent.ThreadPoolExecutor$Worker.run (ThreadPoolExecutor.java:635)
    java.lang.Thread.run (Thread.java:840)

25-09-08 09:18:26:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:26:794 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-08 09:18:27:815 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:27:816 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-f6acd13c-42a6-4d1a-bc48-a68e02589207","MsgBody":{}}
25-09-08 09:18:27:824 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-08 09:18:27:858 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:27:859 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:27:863 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:27:864 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:27:868 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:27:871 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:239 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:38:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-08 09:18:38:246 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:38:248 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:18:38:253 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-08 09:18:38:428 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:38:430 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-08 09:18:38:532 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:534 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:541 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:18:38:542 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"cbccd622-f63a-47fc-aaa9-543d1983fb5b","Result":true}
25-09-08 09:18:38:548 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-08 09:18:38:643 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:645 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:654 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:656 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:664 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:665 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:670 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:671 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:680 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:682 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:694 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:697 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:709 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:711 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:721 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:38:724 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-08 09:18:38:726 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:38:729 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:38:760 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:18:38:770 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"eb93dac5-c647-4ffc-8d42-002a3f6edcde","Result":true}
25-09-08 09:18:38:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:38:776 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-08 09:18:38:783 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-08 09:18:39:011 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:39:013 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:18:39:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-08 09:18:39:124 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-08 09:18:39:125 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-08 09:18:39:127 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:39:128 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-08 09:18:39:134 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:39:135 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:18:39:137 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-08 09:18:39:204 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:18:39:210 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:39:211 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-08 09:18:39:224 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:39:226 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:18:39:227 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-08 09:18:39:293 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:18:42:516 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:42:516 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:42:523 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:42:523 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-08 09:18:42:525 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-08 09:18:42:553 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:18:42:553 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3fe5fe23-5dbf-460c-8c2b-a0b30a1e4a33","Result":true}
25-09-08 09:18:42:697 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:42:698 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:42:850 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:42:851 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-08 09:18:42:853 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-08 09:18:42:880 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:18:42:881 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3fc53a36-eaac-41b5-8e30-6e9a0ae300c7","Result":true}
25-09-08 09:18:42:925 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:42:927 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:42:931 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:42:932 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:42:937 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:42:937 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-08 09:18:42:977 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:42:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-08 09:18:43:086 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:43:087 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-08 09:18:43:122 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:43:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-08 09:18:43:640 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:18:43:641 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-08 09:18:43:643 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-08 09:18:43:805 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:43:805 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:43:809 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:43:810 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:43:814 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:43:814 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:43:819 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:18:43:819 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"154c7153-587e-430e-a3da-c4c16cd690d2","Result":true}
25-09-08 09:18:44:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:44:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:18:45:995 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:18:45:996 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:20:59:990 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-08 09:21:00:101 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:00:102 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:21:00:271 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:00:272 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-08 09:21:00:277 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 09:21:00:278 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-08 09:21:00:374 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:21:00:376 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:00:377 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-08 09:21:00:378 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:00:378 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-08 09:21:00:380 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:00:381 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-08 09:21:00:382 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:00:383 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-08 09:21:00:384 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:00:385 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-08 09:21:00:426 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:00:428 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1889,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1889,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:00:434 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:00:436 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1889,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1889,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:394 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:395 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-f6acd13c-42a6-4d1a-bc48-a68e02589207","MsgBody":{}}
25-09-08 09:21:01:419 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:419 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:425 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:425 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:429 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:429 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:435 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:436 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:439 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:440 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:445 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:446 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:451 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:452 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_zzzdylqdyz\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:462 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:464 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.896318435668945,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.896318435668945,\r\n  \u0022Code\u0022: \u0022input_zzzdl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:473 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:474 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47.10634592795962,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 47.10634592795962,\r\n  \u0022Code\u0022: \u0022input_yzlwjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:483 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:484 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-08 09:21:01:513 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:514 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-08 09:21:01:538 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:539 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:543 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:544 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:548 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:549 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:554 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:555 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 13.85498113632202,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 13.85498113632202,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:560 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:561 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 4.73096923828125,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 4.73096923828125,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:565 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:566 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 2.689631938934326,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 2.689631938934326,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:571 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:01:572 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 17.896318435668945,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 17.896318435668945,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:01:577 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:578 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-08 09:21:01:584 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:586 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-08 09:21:01:590 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-08 09:21:01:655 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:655 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:21:01:702 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-08 09:21:01:703 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-08 09:21:01:704 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-08 09:21:01:705 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:705 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-08 09:21:01:711 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:712 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:21:01:713 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-08 09:21:01:831 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:21:01:836 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:837 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-08 09:21:01:844 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:01:845 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:21:01:846 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-08 09:21:01:905 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:21:16:438 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-08 09:21:16:519 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:16:520 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:21:16:521 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-08 09:21:16:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:21:16:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"7643a6e3-e740-45a2-82f8-54034246a62a","Result":true}
25-09-08 09:21:16:667 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:16:668 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-08 09:21:16:771 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:16:772 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-08 09:21:16:774 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-08 09:21:16:828 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:16:828 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-08 09:21:16:870 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:21:16:871 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"96fb8016-b5b4-4410-b74f-baf9a5a11956","Result":true}
25-09-08 09:21:16:978 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:16:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:16:991 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:16:992 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-08 09:21:17:060 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:17:061 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:17:066 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:17:067 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-08 09:21:17:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:17:076 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:17:081 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:17:082 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-08 09:21:17:090 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:17:091 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:17:095 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:17:095 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-08 09:21:17:103 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:17:104 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:17:110 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:17:111 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-08 09:21:17:122 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:17:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:17:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:17:130 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-08 09:21:17:354 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:17:355 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-426ba70c-a598-491b-ae16-d5a27715763f","MsgBody":{}}
25-09-08 09:21:23:288 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:23:289 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-08 09:21:23:445 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:23:447 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-8d313bcb-d7cc-424e-bb19-d7d82337452b","MsgBody":{}}
25-09-08 09:21:23:457 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:23:458 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-0ac09652-82d1-4130-a028-2e081713c233","MsgBody":{}}
25-09-08 09:21:23:459 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-0ac09652-82d1-4130-a028-2e081713c233不在执行状态, 而是 :aborted
25-09-08 09:21:23:523 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:23:524 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-2d644e51-aac7-477a-99b7-7f3efe700e51","MsgBody":{}}
25-09-08 09:21:36:873 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9 初始化
25-09-08 09:21:36:926 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:36:927 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:21:36:932 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:36:933 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_31","ProcessID":"project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-08 09:21:36:936 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 09:21:36:937 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-08 09:21:36:993 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:21:36:994 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:36:995 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d","MsgBody":{}}
25-09-08 09:21:36:996 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:36:996 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-08 09:21:37:009 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:37:010 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_31","ProcessID":"project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-08 09:21:37:014 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-da399466-da97-449d-b998-0d7a0823cdd0 初始化
25-09-08 09:21:37:078 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:37:079 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:21:37:080 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-08 09:21:37:127 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:21:37:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"159dd041-7265-4fe7-bdc1-c6741565cbe4","Result":true}
25-09-08 09:21:37:414 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:37:415 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"stop-4e11b297-545d-46fc-9d3e-816533d9cbb8","MsgBody":{}}
25-09-08 09:21:37:434 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:37:435 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-da399466-da97-449d-b998-0d7a0823cdd0","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:21:37:437 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-da399466-da97-449d-b998-0d7a0823cdd0  Stop: {:parent {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}}
25-09-08 09:21:37:493 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:21:37:498 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:37:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf","MsgBody":{}}
25-09-08 09:21:37:607 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:37:608 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"SubTaskEvalScript-748b4a68-608f-47f2-9d29-f552a9eddcab","MsgBody":{}}
25-09-08 09:21:37:618 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:37:619 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:21:37:620 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9  Stop: {:parent nil}
25-09-08 09:21:37:680 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:21:39:999 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-08 09:21:40:071 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:072 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:21:40:073 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-08 09:21:40:095 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:21:40:097 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"1eb85427-1904-42e9-9100-afd78db695be","Result":true}
25-09-08 09:21:40:124 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:125 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-08 09:21:40:364 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:365 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-08 09:21:40:367 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-08 09:21:40:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:390 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-08 09:21:40:426 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:21:40:427 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3e349169-bfe6-4515-8672-5289b8b24383","Result":true}
25-09-08 09:21:40:493 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:40:494 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:40:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:500 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-08 09:21:40:561 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:40:562 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:40:566 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:567 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-08 09:21:40:575 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:40:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:40:582 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-08 09:21:40:595 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:40:596 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:40:601 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:601 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-08 09:21:40:610 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:40:611 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:40:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:617 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-08 09:21:40:623 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:21:40:625 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:21:40:635 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:40:636 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-08 09:21:46:266 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:46:267 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f","MsgBody":{}}
25-09-08 09:21:56:531 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:21:56:532 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-ffdc7080-10a2-478e-9add-ad7db595b4ee","MsgBody":{}}
25-09-08 09:22:06:825 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:06:827 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-08 09:22:06:827 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:06:828 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-08 09:22:06:829 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5不在执行状态, 而是 :finished
25-09-08 09:22:07:442 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:07:443 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-00671a03-a007-4507-85a2-e7ad8bcd2c4c","MsgBody":{}}
25-09-08 09:22:07:448 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:07:449 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-08 09:22:07:451 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b不在执行状态, 而是 :aborted
25-09-08 09:22:07:543 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:07:544 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-17b2ea0b-475e-438a-a9e6-987c3d6bd8d7","MsgBody":{}}
25-09-08 09:22:07:677 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:07:678 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-da720ab0-0182-4e66-b915-3462002a8683","MsgBody":{}}
25-09-08 09:22:07:750 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:07:751 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-8ac6111a-8d6d-4963-9383-34d7420e558a","MsgBody":{}}
25-09-08 09:22:07:759 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:07:760 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:22:07:761 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-08 09:22:07:817 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:22:46:003 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee 初始化
25-09-08 09:22:46:060 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:061 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:22:46:063 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器连接失败，请重新连接控制器";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
return true;
25-09-08 09:22:46:088 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:22:46:089 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"477d6f81-4dd4-476e-98b0-39c5aec2957f","Result":true}
25-09-08 09:22:46:113 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:113 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-cc94c47e-def9-4413-a314-5379c15e2989","MsgBody":{}}
25-09-08 09:22:46:355 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:356 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-d38715eb-7301-437d-b693-3e546d3997f9","MsgBody":{}}
25-09-08 09:22:46:360 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var blockline = Model.dVariable["blockline"];
if(blockline==1){
  return true;
}
return false;
25-09-08 09:22:46:381 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:382 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveStartControl-5da849dd-b2c1-47db-8def-f3243aae6634","MsgBody":{}}
25-09-08 09:22:46:409 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:22:46:410 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"d6e57472-8b4b-4d39-9390-ec12749fcfc1","Result":false}
25-09-08 09:22:46:460 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:22:46:461 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:22:46:465 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:466 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-7a9b46e2-ce29-4ac0-a55d-be7b124e697d","MsgBody":{}}
25-09-08 09:22:46:491 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:22:46:492 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:22:46:517 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:518 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-ec32daaf-a89d-4394-9719-b18e311efb7a","MsgBody":{}}
25-09-08 09:22:46:535 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:22:46:536 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:22:46:566 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:567 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-c39d0131-3fdb-46d8-af94-0668968e4756","MsgBody":{}}
25-09-08 09:22:46:614 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:22:46:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:22:46:659 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-b7edfcb1-c345-463c-aacd-c8d2eb206635","MsgBody":{}}
25-09-08 09:22:46:681 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:22:46:681 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:22:46:720 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:722 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-333a9f87-cae8-4b89-ab58-dd716d819b58","MsgBody":{}}
25-09-08 09:22:46:734 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:22:46:735 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_isXieBo\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:22:46:887 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:46:887 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"xiebo-3756a5ec-49f2-4c21-a287-b92e938f5c45","MsgBody":{}}
25-09-08 09:22:47:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:47:071 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-9a1c7b0d-1760-4d5a-90d7-af26123789a5","MsgBody":{}}
25-09-08 09:22:47:120 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:47:121 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-426ba70c-a598-491b-ae16-d5a27715763f","MsgBody":{}}
25-09-08 09:22:52:983 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:52:984 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-8d313bcb-d7cc-424e-bb19-d7d82337452b","MsgBody":{}}
25-09-08 09:22:52:989 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:52:990 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-0ac09652-82d1-4130-a028-2e081713c233","MsgBody":{}}
25-09-08 09:22:52:991 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-0ac09652-82d1-4130-a028-2e081713c233不在执行状态, 而是 :aborted
25-09-08 09:22:53:028 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:53:029 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-2d644e51-aac7-477a-99b7-7f3efe700e51","MsgBody":{}}
25-09-08 09:22:58:943 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:58:944 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-5cdadc8f-47b5-4639-a8b1-d26cbf98093f","MsgBody":{}}
25-09-08 09:22:58:951 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:22:58:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-54a9826e-7e5e-4b46-a94a-3868836dcd6f","MsgBody":{}}
25-09-08 09:23:04:537 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:04:538 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-71a89ec2-eaf7-4ba4-aa56-76c5a8ea8f69","MsgBody":{}}
25-09-08 09:23:04:544 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:04:545 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-43bef327-78fb-4784-9a26-7efed0ab4432","MsgBody":{}}
25-09-08 09:23:04:547 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-43bef327-78fb-4784-9a26-7efed0ab4432不在执行状态, 而是 :aborted
25-09-08 09:23:04:618 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:04:619 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-153bfad7-49bc-43e7-a801-219b119c9104","MsgBody":{}}
25-09-08 09:23:10:172 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:10:174 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-911d94cb-a26d-4dda-bb0b-281270094ee5","MsgBody":{}}
25-09-08 09:23:10:206 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:10:208 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-ffdc7080-10a2-478e-9add-ad7db595b4ee","MsgBody":{}}
25-09-08 09:23:16:189 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:16:191 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"creepSignalCheck-05ef462b-d544-4857-9945-538a503ecb00","MsgBody":{}}
25-09-08 09:23:16:196 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:16:197 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-32891850-ce61-48d8-98cd-20634cf737c7","MsgBody":{}}
25-09-08 09:23:16:198 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-32891850-ce61-48d8-98cd-20634cf737c7不在执行状态, 而是 :aborted
25-09-08 09:23:21:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:345 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-08 09:23:21:345 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:347 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-08 09:23:21:347 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:348 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5不在执行状态, 而是 :finished
25-09-08 09:23:21:348 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5","MsgBody":{}}
25-09-08 09:23:21:349 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:combinedWaveEndControl-07f9477f-ddab-4730-ac75-81ca40937ab5不在执行状态, 而是 :finished
25-09-08 09:23:21:601 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:602 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"waitEvent-00671a03-a007-4507-85a2-e7ad8bcd2c4c","MsgBody":{}}
25-09-08 09:23:21:606 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:608 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b","MsgBody":{}}
25-09-08 09:23:21:609 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daq-a47ba0de-3727-4ea7-a523-2fd2fbd8fb7b不在执行状态, 而是 :aborted
25-09-08 09:23:21:639 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:640 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-17b2ea0b-475e-438a-a9e6-987c3d6bd8d7","MsgBody":{}}
25-09-08 09:23:21:727 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:728 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-da720ab0-0182-4e66-b915-3462002a8683","MsgBody":{}}
25-09-08 09:23:21:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"SubTaskEvalScript-8ac6111a-8d6d-4963-9383-34d7420e558a","MsgBody":{}}
25-09-08 09:23:21:752 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:23:21:753 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:23:21:754 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-08 09:23:21:802 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:25:41:065 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f 初始化
25-09-08 09:25:41:109 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:110 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:25:41:114 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:115 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_31","ProcessID":"project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f","SubTaskID":"onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"952ef7e6-11e2-4006-a1dd-ab5ac9fecd98"}}
25-09-08 09:25:41:118 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98 初始化
25-09-08 09:25:41:218 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:219 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f","SubTaskID":"onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792","MsgBody":{}}
25-09-08 09:25:41:220 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_yzlwbs").Value;
25-09-08 09:25:41:223 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:224 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:25:41:225 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本: // 获取当前试样名称

        string specimenName = Model.CurrentInst.SampleType;
        // 获取用于报告错误信息的模型变量
        var errorInfoVar = Model.GetVarByName<TextInputVar>("input_SYJY");

        // 定义参数的本地变量
        double width, thickness, notchLength, notchWidth, elasticModulus, yieldStrength, span;

        // 根据试样类型执行不同校验逻辑
        if (specimenName == "sampleType_jincou")
        {
            try
            {
                // --- 校验 试样宽度 ---
                object rawWidth = Model.CurrentInst.Parameters["sampleParam_width"].Value;
                if (rawWidth == null) { errorInfoVar.Value = "请填写试样宽度"; return false; }
                width = Math.Abs(Convert.ToDouble(rawWidth)); 
                if (width == 0) { errorInfoVar.Value = "请填写试样宽度"; return false; } 

                // --- 校验 试样厚度 ---
                object rawThickness = Model.CurrentInst.Parameters["sampleParam_thickness"].Value;
                if (rawThickness == null) { errorInfoVar.Value = "请填写试样厚度"; return false; }
                thickness = Math.Abs(Convert.ToDouble(rawThickness));
                if (thickness == 0) { errorInfoVar.Value = "请填写试样厚度"; return false; } 

                // --- 校验 缺口长度 ---
                object rawNotchLength = Model.CurrentInst.Parameters["sampleParam_notchLength"].Value;
                if (rawNotchLength == null) { errorInfoVar.Value = "请填写缺口长度"; return false; }
                notchLength = Math.Abs(Convert.ToDouble(rawNotchLength)); 
                if (notchLength == 0) { errorInfoVar.Value = "请填写缺口长度"; return false;} 

                // --- 校验 缺口宽度 ---
                object rawNotchWidth = Model.CurrentInst.Parameters["sampleParam_notchWidth"].Value;
                if (rawNotchWidth == null) { errorInfoVar.Value = "请填写缺口宽度"; return false; }
                notchWidth = Math.Abs(Convert.ToDouble(rawNotchWidth));
                // 缺口宽度的0值校验在后续的具体规则中处理，此处不检查

                // --- 校验 弹性模量 ---
                object rawElasticModulus = Model.CurrentInst.Parameters["sampleParam_elasticModulus"].Value;
                if (rawElasticModulus == null) { errorInfoVar.Value = "请填写弹性模量"; return false; }
                elasticModulus = Math.Abs(Convert.ToDouble(rawElasticModulus)); 
                if (elasticModulus == 0) { errorInfoVar.Value = "请填写弹性模量"; return false;}

                // --- 校验 屈服强度 ---
                object rawYieldStrength = Model.CurrentInst.Parameters["sampleParam_yieldStrength"].Value;
                if (rawYieldStrength == null) { errorInfoVar.Value = "请填写屈服强度"; return false; }
                yieldStrength = Math.Abs(Convert.ToDouble(rawYieldStrength));
                if (yieldStrength == 0) { errorInfoVar.Value = "请填写屈服强度"; return false; } 
            }
            catch (Exception)
            {
                // 捕获Convert.ToDouble可能出现的格式转换异常
                errorInfoVar.Value = "参数输入了非法值，请输入数字。"; 
                return false;
            }

            // 执行跨参数的特定逻辑校验
            // 规则：缺口长度与试样宽度之比应大于等于0.2
            if (notchLength / width < 0.2)
            {
               errorInfoVar.Value = "缺口长度与试样宽度比小于0.2"; 
                return false;
            }

            // 规则：缺口宽度应小于0.1*试样宽度
            if (notchWidth >= 0.1 * width)
            {
                errorInfoVar.Value = "缺口宽度应小于0.1*W"; 
                return false;
            }

            // 规则：但不应小于1.6mm
            if (notchWidth < 1.6)
            {
               errorInfoVar.Value = "缺口宽度应大于1.6mm"; 
                return false;
            }
        }
        else if (specimenName == "sampleType_danbian")
        {
            try
            {
                // --- 校验 试样宽度 ---
                object rawWidth = Model.CurrentInst.Parameters["sampleParam_width"].Value;
                if (rawWidth == null) { errorInfoVar.Value = "请填写试样宽度"; return false; }
              width = Math.Abs(Convert.ToDouble(rawWidth));
                if (width == 0) { errorInfoVar.Value = "请填写试样宽度"; return false; } 

                // --- 校验 试样厚度 ---
                object rawThickness = Model.CurrentInst.Parameters["sampleParam_thickness"].Value;
                if (rawThickness == null) { errorInfoVar.Value = "请填写试样厚度"; return false; }
              thickness = Math.Abs(Convert.ToDouble(rawThickness)); 
                if (thickness == 0) { errorInfoVar.Value = "请填写试样厚度"; return false; } 

                // --- 校验 缺口长度 ---
                object rawNotchLength = Model.CurrentInst.Parameters["sampleParam_notchLength"].Value;
                if (rawNotchLength == null) { errorInfoVar.Value = "请填写缺口长度"; return false; }
               notchLength = Math.Abs(Convert.ToDouble(rawNotchLength)); 
                if (notchLength == 0) { errorInfoVar.Value = "请填写缺口长度"; return false;} 

                // --- 校验 缺口宽度 ---
                object rawNotchWidth = Model.CurrentInst.Parameters["sampleParam_notchWidth"].Value;
                if (rawNotchWidth == null) { errorInfoVar.Value = "请填写缺口宽度"; return false; }
              notchWidth = Math.Abs(Convert.ToDouble(rawNotchWidth)); 

                // --- 校验 弹性模量 ---
                object rawElasticModulus = Model.CurrentInst.Parameters["sampleParam_elasticModulus"].Value;
                if (rawElasticModulus == null) { errorInfoVar.Value = "请填写弹性模量"; return false; }
             elasticModulus = Math.Abs(Convert.ToDouble(rawElasticModulus)); 
                if (elasticModulus == 0) { errorInfoVar.Value = "请填写弹性模量"; return false; }

                // --- 校验 屈服强度 ---
                object rawYieldStrength = Model.CurrentInst.Parameters["sampleParam_yieldStrength"].Value;
                if (rawYieldStrength == null) { errorInfoVar.Value = "请填写屈服强度"; return false; }
              yieldStrength = Math.Abs(Convert.ToDouble(rawYieldStrength)); 
                if (yieldStrength == 0) { errorInfoVar.Value = "请填写屈服强度"; return false; }

                // --- 校验 试样跨距 ---
                object rawSpan = Model.CurrentInst.Parameters["sampleParam_span"].Value;
                if (rawSpan == null) { errorInfoVar.Value = "请填写试样跨距"; return false; }
              span = Math.Abs(Convert.ToDouble(rawSpan)); 
                if (span == 0) { errorInfoVar.Value = "请填写试样跨距"; return false; } 
            }
            catch (Exception)
            {
                // 捕获Convert.ToDouble可能出现的格式转换异常
                errorInfoVar.Value = "参数输入了非法值，请输入数字。";
                return false;
            }

            // 执行跨参数的特定逻辑校验
            // 规则：缺口长度与试样宽度之比应大于等于0.2
            if (notchLength / width < 0.2)
            {
              errorInfoVar.Value = "缺口长度与试样宽度比小于0.2"; 
                return false;
            }

            // 规则：缺口宽度应小于0.1*试样宽度
            if (notchWidth >= 0.1 * width)
            {
              errorInfoVar.Value = "缺口宽度应小于0.1*W";
                return false;
            }

            // 规则：但不应小于1.6mm
            if (notchWidth < 1.6)
            {
                errorInfoVar.Value = "缺口宽度应大于1.6mm"; 
                return false;
            }
        }
        else
        {
            // 未知试样类型
            errorInfoVar.Value = "未知的试样类型：" + specimenName;
            return false;
        }

        // 所有校验均通过
        return true;
25-09-08 09:25:41:264 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:41:265 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"b3eda46d-b234-40ec-8f63-2b802028cb5e","Result":false}
25-09-08 09:25:41:269 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-08 09:25:41:338 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:41:339 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"7824c123-c762-4906-bc00-48074fc30043","Result":true}
25-09-08 09:25:41:354 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:41:355 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"8ac2932d-9e77-4691-b404-74ce9536e579","Result":true}
25-09-08 09:25:41:360 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本://预制裂纹重新开始标识
return Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
25-09-08 09:25:41:360 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:361 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:25:41:362 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98  Stop: {:parent {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f", :SubTaskID "onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "952ef7e6-11e2-4006-a1dd-ab5ac9fecd98"}}}
25-09-08 09:25:41:432 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:25:41:442 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:41:443 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"c43e4ebd-fa0b-44f8-bb64-7fbe3b37559a","Result":true}
25-09-08 09:25:41:444 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:445 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f","SubTaskID":"onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792","MsgBody":{}}
25-09-08 09:25:41:447 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792不在执行状态, 而是 :finished
25-09-08 09:25:41:562 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:41:563 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 17.25624758842134,\r\n  \u0022Dimension\u0022: \u0022dimension_Displacement\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw4b9c1d4\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022mm\u0022,\r\n  \u0022DisplayValue\u0022: 17.25624758842134,\r\n  \u0022Code\u0022: \u0022input_precast_crack_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:41:569 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:41:570 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.7256247588421338,\r\n  \u0022Dimension\u0022: \u0022dimension_Displacement\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw4b9c1d4\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022mm\u0022,\r\n  \u0022DisplayValue\u0022: 1.7256247588421338,\r\n  \u0022Code\u0022: \u0022input_precast_crack_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:41:575 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f","SubTaskID":"SubTaskEvalScript-97de76fa-a20e-4569-9ec8-cfd75bf8e4cb","MsgBody":{}}
25-09-08 09:25:41:593 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:41:594 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 09:25:41:595 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f  Stop: {:parent nil}
25-09-08 09:25:41:720 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 09:25:43:665 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-08 09:25:43:761 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:43:762 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 09:25:43:764 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-08 09:25:43:786 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:43:788 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:43:792 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:43:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"aaa33d98-2c6c-4d52-a9ee-23699ae330cd","Result":true}
25-09-08 09:25:45:107 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:45:108 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:45:112 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:45:113 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-08 09:25:45:114 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-08 09:25:45:134 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:45:134 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"e32d58fe-037e-47eb-941b-52bd53bfca1f","Result":true}
25-09-08 09:25:45:158 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:45:158 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:45:162 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:45:163 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-08 09:25:45:164 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-08 09:25:45:187 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:45:188 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"f18de07b-14e7-41f6-8caf-37219d8aa6aa","Result":true}
25-09-08 09:25:45:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:45:297 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:45:301 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:45:302 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:45:309 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:45:310 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-08 09:25:45:327 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:45:328 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-08 09:25:45:350 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:45:350 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-08 09:25:45:374 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:45:375 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-08 09:25:45:887 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 09:25:45:888 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-08 09:25:45:889 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-08 09:25:45:917 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:45:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:45:922 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:45:923 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:45:927 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:45:928 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:45:932 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-08 09:25:45:932 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"10cb7dcd-3fe4-42c1-a5ab-ad615ef222df","Result":true}
25-09-08 09:25:46:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:46:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 09:25:48:228 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 09:25:48:229 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 10:20:40:426 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-08 10:20:40:497 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-08 10:20:40:508 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:40:508 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 10:20:40:550 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:552 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-08 10:20:40:554 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:555 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:555 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9  Stop: {:parent nil}
25-09-08 10:20:40:559 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:561 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:562 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-08 10:20:40:568 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:570 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:571 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-da399466-da97-449d-b998-0d7a0823cdd0  Stop: {:parent {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}}
25-09-08 10:20:40:575 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:575 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:576 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-08 10:20:40:663 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:664 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:665 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98  Stop: {:parent {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f", :SubTaskID "onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "952ef7e6-11e2-4006-a1dd-ab5ac9fecd98"}}}
25-09-08 10:20:40:667 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:668 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:669 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-08 10:20:40:732 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:734 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:736 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f  Stop: {:parent nil}
25-09-08 10:20:40:741 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:743 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:744 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee  Stop: {:parent nil}
25-09-08 10:20:40:748 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:749 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:750 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-08 10:20:40:835 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:836 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:837 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-08 10:20:40:899 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:40:901 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 10:20:40:902 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-08 10:20:40:904 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 10:20:41:418 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-08 10:20:41:419 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-08 10:20:41:420 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-08 10:20:41:420 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:41:422 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-08 10:20:41:422 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:41:423 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 10:20:41:937 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:41:937 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daq-d5711df9-3137-4a67-9444-060b64552e70","MsgBody":{}}
25-09-08 10:20:41:939 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:41:939 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-08 10:20:42:044 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:42:045 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-08 10:20:42:059 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:42:059 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-08 10:20:42:060 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:42:060 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-08 10:20:42:061 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:20:42:061 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-08 10:20:42:282 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 10:20:42:283 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47173,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 47173,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 10:20:42:289 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 10:20:42:290 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47173,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 47173,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 10:25:51:449 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-08 10:25:51:450 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"2a723568-2f8f-4dfe-95a8-09f0b4d1a22f","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-08 10:25:52:648 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-08 10:25:52:702 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:25:52:704 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 10:25:54:462 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 10:25:54:463 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 10:25:54:469 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 10:25:54:469 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 10:25:54:475 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:25:54:475 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-08 10:25:54:480 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 10:25:54:480 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 10:25:54:481 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-08 10:25:54:523 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-08 11:05:23:311 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-08 11:05:23:390 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-08 11:05:23:430 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-08 18:07:08:363 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-08 18:07:08:364 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"81d83f07-7dcd-45d9-b271-640c795af782","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-08 18:07:23:692 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-08 18:07:23:883 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 18:07:23:884 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-08 18:07:25:266 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 18:07:25:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 18:07:25:274 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-08 18:07:25:275 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-08 18:07:25:283 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 18:07:25:284 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-08 18:07:25:326 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-08 18:07:25:327 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-08 18:07:25:329 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-08 18:07:25:385 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
