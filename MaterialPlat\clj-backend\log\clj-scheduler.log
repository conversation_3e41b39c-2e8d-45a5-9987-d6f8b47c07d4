25-09-09 09:19:57:814 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 09:19:57:883 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 09:19:57:890 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:19:57:892 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:19:57:966 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:19:57:967 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:19:57:971 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:19:57:973 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:19:57:974 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:19:58:035 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:19:58:036 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:19:58:039 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:19:58:226 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:19:58:570 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 09:19:58:572 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 09:19:58:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 09:19:58:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:19:58:575 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:19:58:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:19:58:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:19:58:704 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:19:58:705 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:22:35:322 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 09:22:35:323 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"4d050c03-4424-4954-8c4f-2be789b89828","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 09:22:36:452 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 09:22:36:607 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:36:608 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:22:38:050 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:38:051 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 09:22:38:062 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:38:063 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:22:38:064 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:22:38:106 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:22:40:822 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 09:22:40:861 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:40:862 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:22:40:908 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:40:909 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 09:22:42:776 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 09:22:44:497 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:44:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:22:44:504 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:44:505 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:22:44:507 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 09:22:44:568 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:44:570 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:44:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:44:578 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:22:44:909 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:44:910 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:22:44:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:44:919 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:22:44:920 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:22:44:986 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:22:45:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:45:232 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:22:45:650 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:45:651 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:46:701 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 09:22:46:839 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:46:842 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:22:46:845 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:22:46:914 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:46:915 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:46:919 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:22:46:920 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"c9678066-3874-4ecf-9867-85e612c1d836","Result":true}
25-09-09 09:22:46:925 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:22:46:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:46:953 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:46:958 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:22:46:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"9582113d-0f84-4e73-a85d-c99f47b3c828","Result":true}
25-09-09 09:22:48:175 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:48:176 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:48:181 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:48:181 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 09:22:48:183 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 09:22:48:216 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:22:48:217 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3f63d90d-dd1a-48d0-8a87-9661a984664f","Result":true}
25-09-09 09:22:48:408 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:48:409 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:48:615 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:48:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 09:22:48:619 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 09:22:48:656 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:22:48:657 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"ac4b1446-bfed-4a02-9a2c-b7b28793e799","Result":true}
25-09-09 09:22:52:682 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:52:683 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:52:687 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:52:688 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:52:693 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:52:693 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 09:22:52:739 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:52:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 09:22:52:877 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:52:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 09:22:52:946 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:52:947 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 09:22:53:475 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:22:53:476 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 09:22:53:477 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 09:22:53:760 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:53:761 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:53:765 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:53:766 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:53:771 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:53:772 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:53:776 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:22:53:777 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3cb521a2-bc51-4987-bb1b-d97a9316bb93","Result":true}
25-09-09 09:22:54:372 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:54:373 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:22:56:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:22:56:345 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:24:19:770 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 09:24:19:820 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 09:24:19:856 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:19:857 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:24:20:033 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 09:24:20:034 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 09:24:20:034 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 09:24:20:035 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:036 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:24:20:036 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:037 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:24:20:042 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:042 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:24:20:043 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:24:20:088 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:24:20:089 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:24:20:098 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:24:20:099 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:24:20:101 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:104 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:105 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-09 09:24:20:125 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:126 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:24:20:127 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:24:20:130 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:24:20:131 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:24:20:135 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:136 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:24:20:138 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 09:24:20:314 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:342 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:343 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:24:20:345 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:24:20:348 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:350 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:24:20:351 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 09:24:20:411 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:412 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:414 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:24:20:414 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 09:24:20:415 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:24:20:418 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:419 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:24:20:420 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:24:20:422 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:24:20:525 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:526 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 09:24:20:537 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:538 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 09:24:20:540 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:540 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 09:24:20:541 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:24:20:541 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 09:24:20:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:24:20:650 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1182,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1182,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:24:20:658 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:24:20:659 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1182,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1182,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:16:322 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 09:25:16:324 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"9c48444c-a137-4844-9c91-3de5d50271cb","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 09:25:17:468 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 09:25:17:593 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:17:594 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:25:17:862 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:17:863 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:17:868 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:17:870 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:17:876 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:17:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 09:25:17:920 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:17:921 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:25:17:922 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:25:18:052 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:25:27:148 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 09:25:27:183 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:27:184 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:25:27:223 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:27:224 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 09:25:30:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:30:617 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:25:30:619 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:30:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:25:30:621 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 09:25:30:659 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:30:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:30:907 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:30:908 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:31:410 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:31:410 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:32:101 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 09:25:32:145 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:32:146 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:25:32:160 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:32:161 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:25:32:170 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:32:171 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:25:32:172 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:25:32:284 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:25:32:473 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:32:474 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:25:32:899 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:32:900 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:36:602 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 09:25:36:656 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:36:657 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:25:36:658 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:25:36:744 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:36:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:36:749 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:25:36:750 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"77f90f4e-efad-4ffe-b2e8-c9f839ba99d4","Result":true}
25-09-09 09:25:37:961 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:37:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:37:969 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:37:970 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 09:25:37:972 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 09:25:38:009 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:25:38:011 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3a61df3d-1d05-45c5-b5ac-11866a94d984","Result":true}
25-09-09 09:25:38:151 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:38:152 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:38:323 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:38:324 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 09:25:38:326 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 09:25:38:358 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:25:38:359 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"5a2990e4-06bc-4731-aa40-ccbf663980ed","Result":true}
25-09-09 09:25:38:439 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:38:440 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:38:444 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:38:445 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:38:449 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:38:450 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 09:25:38:498 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:38:498 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 09:25:38:625 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:38:626 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 09:25:38:696 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:38:697 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 09:25:39:219 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:25:39:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 09:25:39:222 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 09:25:39:494 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:39:495 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:39:499 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:39:500 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:39:504 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:39:505 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:39:509 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:25:39:510 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"54793eea-4c64-4a93-9da1-ebc02b6c4d25","Result":true}
25-09-09 09:25:39:958 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:39:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:25:41:997 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:25:41:997 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:26:11:541 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 09:26:11:595 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 09:26:11:598 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:11:599 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:26:11:722 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 09:26:11:724 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 09:26:11:725 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 09:26:11:726 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:11:727 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:26:11:729 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:11:730 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:26:11:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:11:747 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:26:11:750 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:26:11:826 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:26:11:828 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:26:11:843 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:26:11:845 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:26:11:864 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:11:866 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-09 09:26:11:875 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:11:884 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:11:885 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:26:11:886 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:26:11:891 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:26:11:892 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:26:11:896 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:11:898 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:26:11:899 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 09:26:12:083 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:12:095 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:12:097 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:26:12:099 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:26:12:102 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:12:104 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:26:12:105 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 09:26:12:149 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:12:150 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 09:26:12:237 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:12:239 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:26:12:240 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:26:12:244 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:12:245 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:26:12:246 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:26:12:249 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:26:12:257 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:12:259 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 09:26:12:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:12:261 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 09:26:12:262 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:12:263 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 09:26:12:307 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:26:12:308 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 09:26:12:463 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:26:12:464 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 428,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 428,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:26:12:470 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:26:12:471 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 428,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 428,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:45:992 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 09:29:45:993 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"d75d08b9-c33d-4f93-ae91-dd2da28bd3c3","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 09:29:47:262 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 09:29:47:416 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:47:417 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:29:47:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:29:47:776 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:47:780 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:29:47:781 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:47:784 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:47:785 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 09:29:47:820 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:47:821 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:29:47:822 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:29:47:861 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:29:52:156 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 09:29:52:191 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:52:192 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:29:52:225 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:52:226 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 09:29:55:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:55:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:29:55:577 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:55:579 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:29:55:580 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 09:29:55:623 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:29:55:624 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:55:889 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:29:55:890 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:56:388 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:29:56:388 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:57:174 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 09:29:57:226 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:57:227 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:29:57:242 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:57:243 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:29:57:267 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:57:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:29:57:270 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:29:57:323 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:29:57:553 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:57:554 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:29:57:890 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:29:57:891 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:59:808 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 09:29:59:864 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:29:59:865 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:29:59:866 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:29:59:937 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:29:59:938 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:29:59:942 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:29:59:942 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"7a342d76-22e2-4d21-aa01-2d634379dee7","Result":true}
25-09-09 09:30:01:535 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:01:536 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:01:540 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:30:01:541 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 09:30:01:542 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 09:30:01:578 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:30:01:579 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3e008b04-1c6a-4c7c-8403-9d7329786452","Result":true}
25-09-09 09:30:01:741 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:01:742 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:01:901 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:30:01:902 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 09:30:01:903 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 09:30:01:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:30:01:954 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"5018dbb6-0e16-4d14-a9b5-03d2063f3a6d","Result":true}
25-09-09 09:30:02:038 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:02:039 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:02:043 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:02:044 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:02:049 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:30:02:050 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 09:30:02:104 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:30:02:105 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 09:30:02:238 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:30:02:239 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 09:30:02:323 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:30:02:324 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 09:30:02:846 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:30:02:847 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 09:30:02:848 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 09:30:03:035 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:03:036 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:03:040 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:03:041 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:03:045 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:03:045 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:03:050 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:30:03:051 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"976a63c0-2415-4315-9e11-af1cd942c9fa","Result":true}
25-09-09 09:30:03:508 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:03:509 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:30:05:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:30:05:577 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:31:34:568 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 09:31:34:653 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 09:31:34:656 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:34:657 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:31:34:811 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 09:31:34:813 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 09:31:34:814 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 09:31:34:815 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:34:816 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:31:34:817 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:34:818 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:31:34:825 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:34:826 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:31:34:827 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:31:34:951 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:31:34:953 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:31:34:971 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:31:34:972 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:34:972 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:31:34:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:34:981 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-09 09:31:35:027 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:35:028 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:31:35:030 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:31:35:034 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:31:35:035 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:31:35:038 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:35:039 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:31:35:041 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 09:31:35:163 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:35:164 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:35:165 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:31:35:165 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:31:35:168 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:35:169 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:31:35:169 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 09:31:35:184 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:35:185 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 09:31:35:202 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:35:203 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 09:31:35:204 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6不在执行状态, 而是 :aborted
25-09-09 09:31:35:216 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:35:217 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 09:31:35:218 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7不在执行状态, 而是 :aborted
25-09-09 09:31:35:229 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:35:230 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 09:31:35:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:31:35:231 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daqCycle-55422a18-1e18-4163-96d7-92687306d8e7不在执行状态, 而是 :aborted
25-09-09 09:31:35:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 09:31:35:233 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc不在执行状态, 而是 :aborted
25-09-09 09:31:35:243 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:35:244 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:31:35:244 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:31:35:247 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:35:247 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:31:35:248 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:31:35:250 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:31:35:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:31:35:269 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1262,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1262,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:31:35:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:31:35:274 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1262,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1262,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:23:040 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 09:33:23:041 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"acf894ee-e8a5-4a50-bc00-7400280f9504","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 09:33:24:240 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 09:33:24:523 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:24:524 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:33:24:798 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:24:799 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:24:803 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:24:804 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:24:808 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:24:808 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 09:33:24:853 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:24:854 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:33:24:856 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:33:24:898 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:33:27:261 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 09:33:27:298 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:27:299 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:33:27:336 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:27:337 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 09:33:30:704 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:30:705 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:33:30:709 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:30:710 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:33:30:710 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 09:33:30:752 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:30:753 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:31:033 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:31:034 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:31:534 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:31:536 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:33:214 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 09:33:33:272 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:33:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:33:33:290 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:33:291 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:33:33:336 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:33:337 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:33:33:338 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:33:33:387 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:33:33:531 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:33:532 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:33:597 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:33:598 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:33:35:612 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 09:33:35:650 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:35:651 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:33:35:652 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:33:35:716 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:35:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:35:723 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:33:35:723 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"c42e81ed-3520-405a-8e70-faf3c9a173c3","Result":true}
25-09-09 09:33:37:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:37:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:37:079 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:37:080 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 09:33:37:081 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 09:33:37:114 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:33:37:115 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"aa4c9436-a715-4ad8-9236-de3af1142f25","Result":true}
25-09-09 09:33:37:303 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:37:304 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:37:460 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:37:462 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 09:33:37:463 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 09:33:37:531 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:33:37:532 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"7ebe64f4-4dfe-47df-a2c7-281f60dbc451","Result":true}
25-09-09 09:33:37:636 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:37:636 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:37:643 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:37:644 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:37:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:37:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 09:33:37:702 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:37:703 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 09:33:37:825 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:37:826 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 09:33:37:898 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:37:899 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 09:33:38:416 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:33:38:417 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 09:33:38:418 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 09:33:38:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:38:584 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:38:589 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:38:589 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:38:593 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:38:594 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:38:597 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:33:38:598 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"32578970-8f95-4a48-b5b9-1fed78b3accf","Result":true}
25-09-09 09:33:39:016 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:39:017 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:33:40:898 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:33:40:899 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:35:07:183 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 09:35:07:265 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 09:35:07:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:269 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:35:07:398 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 09:35:07:399 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 09:35:07:400 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 09:35:07:401 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:402 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:35:07:403 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:405 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:35:07:431 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:431 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:35:07:433 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:35:07:577 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:07:732 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:35:07:733 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:35:07:743 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:35:07:744 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:35:07:749 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:750 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-09 09:35:07:755 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:756 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:35:07:757 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:35:07:761 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:35:07:762 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:35:07:766 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:07:767 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:35:07:769 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 09:35:07:897 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:07:913 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:07:914 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:35:07:914 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:35:07:917 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:07:918 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:35:07:919 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 09:35:07:934 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:935 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 09:35:07:995 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:07:996 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 09:35:07:997 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6不在执行状态, 而是 :aborted
25-09-09 09:35:08:019 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:08:019 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 09:35:08:020 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:08:021 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 09:35:08:021 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7不在执行状态, 而是 :aborted
25-09-09 09:35:08:022 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:35:08:023 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 09:35:08:023 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daqCycle-55422a18-1e18-4163-96d7-92687306d8e7不在执行状态, 而是 :aborted
25-09-09 09:35:08:024 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc不在执行状态, 而是 :aborted
25-09-09 09:35:08:032 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:08:032 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:35:08:034 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:35:08:036 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:08:037 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:35:08:038 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:35:08:041 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:35:08:087 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:35:08:088 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1203,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1203,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:35:08:092 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:35:08:093 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1203,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1203,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:04:681 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 09:37:04:682 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"018475ba-3bdb-4cf0-9d41-10193cbaf85b","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 09:37:05:836 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 09:37:05:988 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:05:989 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:37:06:291 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:06:292 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:06:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:06:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:06:300 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:06:300 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 09:37:06:338 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:06:339 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:37:06:340 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:37:06:392 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:37:08:631 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 09:37:08:673 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:08:674 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:37:08:711 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:08:712 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 09:37:12:150 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:12:151 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:37:12:158 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:12:159 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:37:12:160 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 09:37:12:199 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:12:200 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:13:507 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 09:37:13:544 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:13:544 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:37:13:563 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:13:564 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:37:13:588 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:13:589 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:37:13:590 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:37:13:648 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:37:13:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:13:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:37:14:387 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:14:388 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:15:446 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 09:37:15:510 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:15:511 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:37:15:512 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:37:15:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:15:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:15:579 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:37:15:579 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"bc47aea4-776e-4059-a98f-b403824ac0ab","Result":true}
25-09-09 09:37:17:024 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:17:025 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:17:034 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:17:036 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 09:37:17:037 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 09:37:17:068 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:37:17:069 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"3f46b53e-26eb-4db2-9b60-e14ff12ebad4","Result":true}
25-09-09 09:37:17:245 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:17:246 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:17:400 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:17:401 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 09:37:17:403 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 09:37:17:452 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:37:17:452 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"87bb682d-7824-4274-80ba-b0785703f17a","Result":true}
25-09-09 09:37:17:559 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:17:560 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:17:564 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:17:564 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:17:568 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:17:569 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 09:37:17:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:17:621 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 09:37:17:723 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:17:724 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 09:37:17:888 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:17:889 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 09:37:18:429 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:37:18:429 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 09:37:18:430 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 09:37:18:651 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:18:652 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:18:656 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:18:657 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:18:661 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:18:661 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:18:665 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:37:18:666 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"668e1d5b-9361-4745-b812-51a8b399a530","Result":true}
25-09-09 09:37:19:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:19:130 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:37:21:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:37:21:221 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:38:33:156 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 09:38:47:739 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 09:38:47:784 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:47:785 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:38:48:387 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 09:38:48:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 09:38:48:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 09:38:48:390 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:48:391 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:38:48:391 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:48:392 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:38:48:398 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:48:399 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:38:48:401 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:38:48:525 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:48:550 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:38:48:552 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:38:48:561 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:38:48:562 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:38:48:567 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:48:568 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-09 09:38:48:589 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:48:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:38:48:592 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:38:48:598 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:38:48:599 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:38:48:603 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:48:605 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:38:48:607 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 09:38:48:829 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:48:833 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:48:834 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:38:48:835 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:38:48:837 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:48:838 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:38:48:840 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 09:38:48:946 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:48:947 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:38:48:947 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:38:48:950 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:48:950 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:38:48:952 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:38:48:954 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:48:955 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 09:38:48:955 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:38:49:007 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:49:009 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 09:38:49:010 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:49:011 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 09:38:49:017 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:49:018 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 09:38:49:020 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:38:49:021 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 09:38:49:049 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:38:49:050 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1126,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1126,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:38:49:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:38:49:056 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1126,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1126,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:13:136 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 09:39:13:137 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"204eb0b8-f6ac-4372-8ce1-86204592bff5","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 09:39:14:306 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 09:39:14:506 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:14:507 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:39:14:805 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:14:805 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:14:810 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:14:810 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:14:816 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:14:816 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 09:39:14:871 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:14:872 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:39:14:873 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:39:14:915 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:39:16:802 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 09:39:16:839 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:16:839 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:39:16:874 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:16:875 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 09:39:20:257 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:20:259 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:39:20:262 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:20:264 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:39:20:265 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 09:39:20:306 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:20:308 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:20:582 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:20:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:21:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:21:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:21:678 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 09:39:21:739 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:21:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:39:21:760 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:21:762 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:39:21:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:21:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:39:21:797 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:39:21:845 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:39:22:073 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:22:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:22:080 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:22:081 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:39:24:836 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 09:39:24:947 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:24:948 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:39:24:950 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:39:25:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:25:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:25:008 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:39:25:008 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"9d5897ab-d1f7-4667-b51e-28b6399f0a5b","Result":true}
25-09-09 09:39:26:251 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:26:253 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:26:257 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:26:258 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 09:39:26:259 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 09:39:26:312 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:39:26:313 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"5e806ec6-d9f5-4d26-8934-6e7e5b9df281","Result":true}
25-09-09 09:39:26:484 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:26:485 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:26:670 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:26:670 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 09:39:26:672 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 09:39:26:712 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:39:26:713 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"8c3685d9-439d-4852-83d5-48f6499c5fa1","Result":true}
25-09-09 09:39:26:813 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:26:814 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:26:818 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:26:819 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:26:825 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:26:826 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 09:39:26:871 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:26:872 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 09:39:27:001 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:27:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 09:39:27:099 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:27:100 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 09:39:27:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:39:27:621 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 09:39:27:623 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 09:39:27:848 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:27:849 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:27:853 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:27:853 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:27:857 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:27:858 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:27:862 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:39:27:862 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"c3bf20b9-807b-4602-bfe6-e85d262508b0","Result":true}
25-09-09 09:39:28:295 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:28:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:39:30:254 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:39:30:255 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:57:52:826 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 09:57:52:973 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 09:57:52:976 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:52:976 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:57:53:209 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 09:57:53:209 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 09:57:53:210 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 09:57:53:210 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:211 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 09:57:53:211 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:212 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:57:53:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:216 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:57:53:218 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:57:53:258 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:57:53:259 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:57:53:267 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:57:53:268 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:57:53:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-09 09:57:53:275 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:281 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:282 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:57:53:283 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:57:53:286 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:57:53:287 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:57:53:292 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:293 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:57:53:294 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 09:57:53:419 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:469 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:470 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:57:53:470 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:57:53:474 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:475 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:57:53:475 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 09:57:53:619 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:621 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:57:53:622 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 09:57:53:623 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:624 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 09:57:53:624 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 09:57:53:627 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:57:53:645 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:646 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 09:57:53:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:793 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 09:57:53:810 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:811 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 09:57:53:813 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:813 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 09:57:53:815 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:57:53:815 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 09:57:53:970 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:57:53:971 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15015,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15015,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:57:53:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:57:53:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15015,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 15015,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:32:142 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 09:58:32:143 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"326c1e44-16f8-4815-b90c-ca587d86ad36","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 09:58:33:339 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 09:58:33:561 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:33:562 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:58:34:003 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:34:003 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:34:008 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:34:008 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:34:012 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:34:012 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 09:58:34:092 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:34:093 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:58:34:094 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 09:58:34:220 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:58:36:577 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 09:58:36:623 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:36:624 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:58:36:658 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:36:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 09:58:40:341 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:40:342 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:58:40:343 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:40:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 09:58:40:345 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 09:58:40:381 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:40:382 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:48:992 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 09:58:49:036 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:49:037 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:58:49:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:49:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:58:49:092 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:49:092 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 09:58:49:093 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 09:58:49:165 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 09:58:49:364 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:49:364 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:49:369 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:49:370 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 09:58:52:962 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 09:58:53:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:53:056 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 09:58:53:057 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 09:58:53:118 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:53:119 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:53:124 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:58:53:125 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"90b01058-6e41-41e8-89df-b74859812cdf","Result":true}
25-09-09 09:58:54:139 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:54:140 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:54:144 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:54:144 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 09:58:54:145 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 09:58:54:180 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:58:54:181 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"20762a65-82c5-47a9-b347-59724fe4997f","Result":true}
25-09-09 09:58:54:344 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:54:345 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:54:543 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:54:544 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 09:58:54:545 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 09:58:54:586 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:58:54:587 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"39476e85-438d-4fc1-bb29-d86a821ef242","Result":true}
25-09-09 09:58:55:376 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:55:377 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:55:382 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:55:382 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:55:388 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:55:388 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 09:58:55:439 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:55:440 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 09:58:55:572 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:55:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 09:58:55:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:55:650 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 09:58:56:168 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 09:58:56:169 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 09:58:56:171 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 09:58:56:349 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:56:350 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:56:354 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:56:355 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:56:360 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:56:361 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:56:364 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 09:58:56:365 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"01bcc960-0274-4ef0-8eb7-a0ebde43961c","Result":true}
25-09-09 09:58:56:856 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:56:857 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 09:58:58:933 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 09:58:58:934 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:16:19:044 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 10:16:19:291 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 10:16:19:310 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:19:311 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:16:19:957 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 10:16:19:957 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 10:16:19:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 10:16:19:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:19:961 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 10:16:19:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:19:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:16:19:967 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:19:968 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 10:16:19:971 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 10:16:20:467 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:16:20:468 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:16:20:569 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:16:20:571 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:16:20:572 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:20:601 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:20:602 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-09 10:16:20:609 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:20:610 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 10:16:20:613 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 10:16:20:628 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:16:20:629 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 10:16:20:632 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:20:639 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:16:20:646 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 10:16:20:962 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:21:017 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:21:019 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:16:21:020 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 10:16:21:024 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:21:026 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:16:21:026 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 10:16:21:084 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:21:086 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 10:16:21:132 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:21:133 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:16:21:134 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 10:16:21:137 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:21:138 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:16:21:139 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 10:16:21:141 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:16:21:292 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:21:293 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 10:16:21:305 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:21:306 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 10:16:21:307 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:21:308 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 10:16:21:309 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:16:21:310 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 10:16:21:376 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:16:21:377 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 14080,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 14080,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:16:21:388 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:16:21:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 14080,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 14080,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:37:795 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-09 10:17:37:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"db716d66-c254-4457-af5d-8e692bdf6401","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-09 10:17:39:026 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-09 10:17:39:135 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:39:136 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:17:39:580 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:39:581 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:39:585 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:39:586 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:39:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:39:591 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-09 10:17:39:624 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:39:626 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 10:17:39:627 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 10:17:39:691 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:17:42:660 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-09 10:17:42:711 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:42:711 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:17:42:751 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:42:752 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-09 10:17:46:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:46:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 10:17:46:243 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:46:244 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-09 10:17:46:245 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-09 10:17:46:303 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:46:305 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:47:846 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-09 10:17:47:900 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:47:900 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:17:47:915 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:47:916 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 10:17:47:960 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:47:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-09 10:17:47:963 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 10:17:48:094 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:17:48:234 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:48:235 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-09 10:17:48:940 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:48:941 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:51:289 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-09 10:17:51:347 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:51:348 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:17:51:350 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-09 10:17:51:438 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:51:440 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:51:447 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 10:17:51:448 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"8a74127a-87f8-45de-898a-4577b35337c1","Result":true}
25-09-09 10:17:52:599 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:52:600 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:52:611 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:52:612 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-09 10:17:52:614 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-09 10:17:52:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 10:17:52:663 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"71887841-da1c-4729-a0e3-efecc064ab46","Result":true}
25-09-09 10:17:52:880 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:52:882 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:53:084 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:53:086 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-09 10:17:53:088 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-09 10:17:53:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 10:17:53:131 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"fffcc19a-33d5-4051-8987-cb1332da0181","Result":true}
25-09-09 10:17:53:940 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:53:941 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:53:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:53:953 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:53:964 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:53:965 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-09 10:17:54:008 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:54:009 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-09 10:17:54:149 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:54:151 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-09 10:17:54:227 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:54:229 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-09 10:17:54:743 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:17:54:744 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-09 10:17:54:745 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-09 10:17:54:951 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:54:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.6896318324019572,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.6896318324019572,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:54:956 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:54:956 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.89631832401957,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.89631832401957,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:54:960 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:54:961 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:54:965 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-09 10:17:54:966 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_31","ScriptId":"40339931-01b0-4c80-98dd-fd8ce0de6718","Result":true}
25-09-09 10:17:55:266 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:55:267 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:17:57:135 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:17:57:136 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 850.8265709574918,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 850.8265709574918,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:18:38:188 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-09 10:18:38:235 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-09 10:18:38:239 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:38:240 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:18:38:288 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:18:38:289 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-09 10:18:38:292 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:18:38:293 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:18:38:294 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-09 10:18:38:369 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:18:38:370 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:18:38:370 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-09 10:18:38:372 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:18:38:373 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:18:38:374 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-09 10:18:38:428 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:18:38:429 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:18:38:432 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-09 10:18:38:478 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:18:38:479 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-09 10:18:38:480 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_31-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-09 10:18:38:560 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-09 10:18:38:613 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-09 10:18:38:614 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":31}
25-09-09 10:18:38:614 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 31}
25-09-09 10:18:38:615 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:38:615 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 10:18:38:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:38:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-09 10:18:38:900 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:38:901 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-09 10:18:39:096 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:39:097 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-09 10:18:39:105 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:39:106 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-09 10:18:39:106 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:39:107 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-09 10:18:39:107 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:39:107 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-09 10:18:39:109 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-09 10:18:39:110 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_31","ProcessID":"project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-09 10:18:39:165 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:18:39:166 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 578,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 578,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-09 10:18:39:174 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-09 10:18:39:175 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 578,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 578,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_31\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
