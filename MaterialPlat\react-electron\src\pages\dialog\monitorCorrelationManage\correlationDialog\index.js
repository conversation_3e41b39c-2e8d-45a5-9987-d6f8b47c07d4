import React, { useEffect, useState } from 'react'
import {
    Tabs, List, Checkbox, Input
} from 'antd'
import { useTranslation } from 'react-i18next'

import VModal from '@/components/vModal/index'
import { delGlobalMapping, postGlobalMapping } from '@/utils/services'

import { Container } from './style'

export const TABS = {
    // 模板: 'template',
    项目: 'project'
}

/**
 * @description 配置关联的弹窗
 * @param open 打开状态
 * @param setOpen 设置状态的函数
 * @param type 当前配置的数据类型 输入信号结果变量或者动作
 * @param globalName 当前选择的全局name
 * @param globalCode 当前选择的全局code
 * @param originalData 模板项目的原始数据
 * @param initalCorrelation 当前该全局code的关联关系,初始化数据和最后对比用
 * @param onSubmit 确认回调
*/
const CorrelationDialog = ({
    open, setOpen, type, globalName, globalCode, originalData, initalCorrelation, onSubmit
}) => {
    const { t } = useTranslation()
    // 项目模板的tabkey
    const [activeKey, setActiveKey] = useState(TABS.项目)
    // 左侧数据源的id,项目或者模板
    const [selectedSourceId, setSelectedSourceId] = useState()
    // 右侧变量列表
    const [variableList, setVariableList] = useState([])
    // 搜索关键词
    const [searchKeyword, setSearchKeyword] = useState('')
    // 当前的关联关系列表
    const [newCorrelation, setNewCorrelation] = useState([
        // {
        //     code: 'string',
        //     project_id: 0,
        //     template_id: 0
        // }
    ])

    // 初始化数据
    useEffect(() => {
        if (open) {
            setNewCorrelation(initalCorrelation)
            setVariableList([])
            setSelectedSourceId()
            setSearchKeyword('')
        }
    }, [initalCorrelation, open])

    // 确定时找出对应新增和删除的关联关系,分别处理
    const onOk = async () => {
        const delMappingIds = initalCorrelation
            .filter(i => {
                return newCorrelation.every(j => j.code !== i.code && (j.project_id !== i.project_id || j.template_id !== i.template_id))
            })
            .map(i => i.id)

        if (delMappingIds.length !== 0) {
            await delGlobalMapping({ ids: delMappingIds })
        }

        const addMapping = newCorrelation
            // 找到新增的关系
            .filter(i => {
                return !initalCorrelation.some(j => j.code === i.code && (j.project_id === i.project_id || j.template_id === i.template_id))
            }).map(i => ({
                ...i,
                type,
                global_code: globalCode
            }))

        if (addMapping.length !== 0) {
            await postGlobalMapping({
                mappings: addMapping
            })
        }

        onSubmit()
    }

    // 点击左侧项目或者模板时,获取对应变量列表
    const onSourceClick = (id) => {
        setSelectedSourceId(id)
        setVariableList(
            originalData?.[activeKey]?.find(item => item.id === id)?.data?.[type] ?? []
        )
    }

    // 选中右侧数据时,进行处理单选
    const onVariableClick = (code) => {
        setNewCorrelation(prev => {
            const index = prev.findIndex(i => i[`${activeKey}_id`] === selectedSourceId && i.code === code)
            if (index > -1) {
                return prev.filter((_, i) => i !== index)
            }
            return [
                // 单选
                ...prev.filter(i => i[`${activeKey}_id`] !== selectedSourceId),
                {
                    code,
                    [`${activeKey}_id`]: selectedSourceId
                }
            ]
        })
    }

    const getModalTime = () => {
        if (globalName && globalCode) {
            return `${t('globalName')} - ${globalCode}`
        }
        return t('关联变量')
    }

    return (
        <VModal
            title={getModalTime()}
            width="40vw"
            open={open}
            onOk={onOk}
            onCancel={() => setOpen(false)}
        >
            <Tabs
                activeKey={activeKey}
                type="card"
                items={
                    Object.entries(TABS).map(([label, key]) => ({ label, key }))
                }
                onChange={(newActivekey) => {
                    setActiveKey(newActivekey)
                    setSelectedSourceId()
                    setVariableList([])
                }}
            />

            <Container>
                <div>
                    <div>
                        { activeKey === TABS.模板 ? '模板' : '项目'}
                        列表
                    </div>
                    <div className="listContainer">
                        <List
                            size="small"
                            bordered
                            dataSource={originalData?.[activeKey]}
                            renderItem={(item) => (
                                <List.Item
                                    style={{
                                        background: selectedSourceId === item.id && 'rgb(218,233,255)'
                                    }}
                                    onClick={() => onSourceClick(item.id)}
                                >
                                    <Checkbox
                                        checked={newCorrelation.find(i => i[`${activeKey}_id`] === item.id)}
                                    >
                                        {item.name}
                                    </Checkbox>
                                </List.Item>
                            )}
                        />

                    </div>
                </div>

                <div>
                    <div>
                        选中
                        { activeKey === TABS.模板 ? '模板' : '项目'}
                        变量列表(单选)
                    </div>
                    <div style={{ marginBottom: 8 }}>
                        <Input.Search
                            placeholder="搜索变量名称"
                            value={searchKeyword}
                            onChange={(e) => setSearchKeyword(e.target.value)}
                            allowClear
                            size="small"
                        />
                    </div>
                    <div className="listContainer">
                        <List
                            size="small"
                            bordered
                            dataSource={variableList.filter(item => item.name.toLowerCase().includes(searchKeyword.toLowerCase()))}
                            renderItem={(item) => (
                                <List.Item>
                                    <Checkbox
                                        checked={newCorrelation.find(i => i[`${activeKey}_id`] === selectedSourceId && i.code === item.code)}
                                        onChange={() => onVariableClick(item.code)}
                                    >
                                        {item.name}
                                    </Checkbox>
                                </List.Item>
                            )}
                        />
                    </div>
                </div>

            </Container>

        </VModal>
    )
}
export default CorrelationDialog
