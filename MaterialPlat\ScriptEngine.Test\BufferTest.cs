using Buffers;

namespace ScriptEngine.Test;

internal class BufferTest
{
    [Test(Description = "环形缓存区测试")]
    public void TestCircleBuffer()
    {
        CircularBuffer<double> cb = new CircularBuffer<double>(10);
        for (int i = 1; i < 25; i++)
        {
            cb.AddValue(i);
            Console.WriteLine("cb的tail：" + cb.Tail);
            Console.WriteLine("环形缓冲区内容" + cb);
        }

        Console.WriteLine("Finish");
    }

    [Test(Description = "不重复循环缓冲区测试")]
    public void TestNonRepeatCircularBuffer()
    {
        NonRepeatCircularBuffer<double> ncb = new NonRepeatCircularBuffer<double>(5);
        for (int i = 1; i <= 3; i++)
        {
            ncb.AddValue(i);
        }
        Console.WriteLine("环形缓冲区内容: " + ncb);// 环形缓冲区内容: 1, 2, 3
        for (int i = 4; i <= 6; i++)
        {
            ncb.AddValue(i);
        }
                Console.WriteLine("环形缓冲区内容: " + ncb);// 环形缓冲区内容: 4, 5, 6

        ncb.AddValue(7);
        Console.WriteLine("环形缓冲区内容: " + ncb);// 环形缓冲区内容: 7
        Console.WriteLine("环形缓冲区内容: " + ncb);// 环形缓冲区内容:


        for (int i = 8; i <= 50; i++)
        {
            ncb.AddValue(i);
        }
        Console.WriteLine("环形缓冲区内容: " + ncb);// 环形缓冲区内容: 46, 47, 48, 49, 50

        Console.WriteLine("Finish");
    }

    [Test(Description = "不重复循环缓冲区测试2")]
    public void TestNonRepeatCircularBuffer2()
    {
        NonRepeatCircularBuffer<double> ncb = new NonRepeatCircularBuffer<double>(5);
        for (int i = 1; i <= 8; i++)
        {
            ncb.AddValue(i);
        }
        Console.WriteLine("环形缓冲区内容: " + ncb);// 环形缓冲区内容: 4, 5, 6, 7, 8

        Console.WriteLine("Finish");
    }

    [Test(Description = "不重复循环缓冲区测试3")]
    public void TestNonRepeatCircularBuffer3()
    {
        NonRepeatCircularBuffer<double> ncb = new NonRepeatCircularBuffer<double>(50);
        for (int i = 0; i <= 100; i++)
        {
            Thread.Sleep(10);
            ncb.AddValue(i);
            Console.WriteLine("最新的三个点：" + string.Join(",", ncb.GetLastTwoValues()));
            if (ncb.Count % 15 == 0)
            {
                var read = ncb.ReadBuffer();
                Console.WriteLine("每次间隔10读取到的内容：" + string.Join(",", read) + "--对应的count：" + ncb.Count + "，对应的读指针和写指针：" + ncb.Tail);
                //Console.WriteLine("最新的三个点：" + string.Join(",", ncb.GetLastThreeValues()));
            }
        }

        Console.WriteLine("Finish");
    }

    [Test(Description = "环形缓冲区测试")]
    public void TestCircleBuffer1()
    {
        CircularBuffer<double> cb = new CircularBuffer<double>(10);
        for (int i = 1; i < 15; i++)
        {
            cb.AddValue(i);
        }
        var result = cb.GetBuffer();
        Console.WriteLine("获取的结果：" + string.Join(",", result));
        Console.WriteLine("获取的结果数量：" + result.Count());

        Console.WriteLine("Finish");
    }

    [Test(Description = "环形缓冲区测试,没有放满的清况")]
    public void TestCircleBuffer2()
    {
        CircularBuffer<double> cb = new CircularBuffer<double>(10);
        for (int i = 1; i < 6; i++)
        {
            cb.AddValue(i);
        }
        var result = cb.GetBuffer();
        Console.WriteLine("获取的结果111：" + string.Join(",", cb));
        Console.WriteLine("获取的结果数量：" + result.Count());
        Console.WriteLine("Finish");
    }

    public record Test(string Name, int Age);
    [Test(Description = "环形缓冲区测试,不Add直接取的情况")]
    public void TestCircleBuffer3()
    {
        CircularBuffer<Test> cb = new CircularBuffer<Test>(10);
        // 引用需要做个处理
        Test defaultTest = new Test("kk", 43);
        var result = cb.GetBuffer();
        Console.WriteLine("获取的结果数量：" + result.Count());
        var c = result.FirstOrDefault() ?? defaultTest;
        Console.WriteLine("获取里面的内容：" + c.Name);

        Console.WriteLine("Finish");
    }
}