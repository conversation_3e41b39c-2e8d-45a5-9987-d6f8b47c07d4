/* eslint-disable no-param-reassign */
/* eslint-disable no-shadow */
import React, {
    useState, useEffect, useRef, useContext
} from 'react'
import { message, Modal, Progress } from 'antd'
import debounce from 'lodash/debounce'
import isEqual from 'lodash/isEqual'
import cloneDeep from 'lodash/cloneDeep'

import { useDispatch, useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { useHotkeys } from 'react-hotkeys-hook'

import {
    PROJECT_SAMPLE_LIST,
    PROJECT_CURRENT_SAMPLE_MULTI
} from '@/redux/constants/project'
import { SUB_TASK_SHORTCUT } from '@/redux/constants/subTask'
import { ALL_SHORTCUT } from '@/components/navbar/constants'
import { color16, randomStr, handleCode } from '@/utils/utils'
import useUnit from '@/hooks/useUnit'
import useSample from '@/hooks/useSample'
import ContextMenu from '@/components/contextMenu2'
import { codePrefix } from '@/utils/codeConstants'
import {
    editSampleTreeList, editSampleAbout, getSamplesList, addSampleDefault,
    addSample, batchDelSample, getExportExcel, getExportCSV, postSampleCopy,
    getExportExcelParams, initExcelExport, appendExcelData, finalizeExcelExport,
    delVideo, deleteInternational, getExportCSVAsync, getCsvExportStatus
} from '@/utils/services'

import { ExclamationCircleFilled } from '@ant-design/icons'
import { getProjectId } from '@/utils/auth'
import useVideo from '@/hooks/useVideo'
import useSplitLayout from '@/hooks/useSplitLayout'
import { useTrigger } from '@/components/formItems/SetActionOrScript/index'
import { addGlobalLoading, updateGlobalLoading, removeGlobalLoading } from '@/redux/action/globalLoading'

import { SampleContainer, ContextMenuContainer } from './style'
import SampleHead from './components/SampleHead/index'
import SampleList from './components/SampleList'
import SampleModal from './components/SampleModal'
import SampleHeadModal from './components/SampleHeadModal'
import {
    INIT_FORM, BUTTON_ADD_TYPE, TYPE, isSample, SampleContent
} from './constant'
import Setting from './setting'
import useExportExcel from './useExportExcel'

// 右键
const ContextMenuRightClick = ({
    domId, layoutConfig, onDisable, onExcel, onCSV, onCopy, setTingHandle
}) => {
    const content = useContext(SampleContent)
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const { t } = useTranslation()

    const [menu, setMenu] = useState()
    const [data, setData] = useState()

    const onClose = () => {
        setMenu(false)
        setData()
    }
    const onBefore = () => {
        if (content.menu.current) {
            setData(content.menu.current)
            setMenu(true)
        }
    }

    return (
        <ContextMenuContainer>
            <ContextMenu
                domId={domId}
                onBefore={onBefore}
                layoutConfig={layoutConfig}
                onClose={onClose}
            >
                <>
                    <div
                        className="unique-content"
                        onClick={() => {
                            setTingHandle()
                        }}
                    >
                        {t('编辑触发事件')}
                    </div>
                </>
                {
                    menu && (
                        <>
                            <div
                                className="unique-content"
                                onClick={() => {
                                    // 使用data?.code判断分组与试样
                                    let c_disabled = !data.disabled
                                    if (!data?.code) {
                                        c_disabled = !data?.children.every(op => op.disabled)
                                    }
                                    if (menu) {
                                        onDisable(c_disabled, data)
                                    }
                                }}
                            >
                                {/* 使用data?.code，判断分组与试样 */}
                                {data?.code
                                    ? t(data?.disabled ? '解除' : '无效')
                                    : t(data?.children.every(op => op.disabled) ? '解除' : '无效')}
                            </div>
                        </>
                    )
                }
                {/* {
                    menu && (
                        <>
                            <div
                                className="unique-content"
                                onClick={() => {
                                    if (menu) {
                                        onCopy()
                                    }
                                }}
                            >
                                {t('复制')}
                            </div>
                        </>
                    )
                } */}
                {
                    (getProjectId() && menu && !openExperiment) && (
                        <>
                            <div
                                className="unique-content"
                                onClick={() => {
                                    if (menu) {
                                        onExcel(data)
                                    }
                                }}
                            >
                                {t('Excel输出')}
                            </div>
                            <div
                                className="unique-content"
                                onClick={() => {
                                    if (menu) {
                                        onCSV(data)
                                    }
                                }}
                            >
                                {t('CSV输出')}
                            </div>
                        </>
                    )
                }

            </ContextMenu>
        </ContextMenuContainer>
    )
}
const Sample = ({ item, id, layoutConfig }) => {
    const [messageApi, contextHolder] = message.useMessage()
    const { initUnitsData } = useUnit()
    const { initVideoData } = useVideo()
    // 0829临时添加
    const {
        initSampleTree,
        updateOptSample,
        initDefaultSample,
        getSamples: samples,
        initSampleAboutList,
        batchUpdateSample
    } = useSample()
    const { t } = useTranslation()
    const dispatch = useDispatch()

    const unitList = useSelector(state => state.global.unitList)

    const sampleData = useSelector(state => state.project.sampleData)
    const sampleList = useSelector(state => state.project.sampleList)
    const sampleAboutList = useSelector(state => state.project.sampleAboutList)
    const optSample = useSelector(state => state.project.optSample)
    const defaultSample = useSelector(state => state.project.defaultSample)
    const multiSample = useSelector(state => state.project.multiSample)

    // 视频列表数据
    const videoList = useSelector(state => state.template.videoList)

    const [data, setData] = useState([])
    const [opt, setOpt] = useState()
    const [selectedKeys, setSelectedKeys] = useState([]) // 选中的试样
    const [open, setOpen] = useState(false)
    const [headOpen, setHeadOpen] = useState(false)
    const [isAdd, setIsAdd] = useState(TYPE.CREATE)
    const menuRef = useRef()

    const { updateLayoutItem } = useSplitLayout()
    const { onEvent } = useTrigger()
    const [config, setConfig] = useState()
    const [settingOpen, setSettingOpen] = useState(false)

    const isShiftDown = useRef(false)
    const isCtrlDown = useRef(false)

    const { onExcel } = useExportExcel({ data, multiSample })

    const timer = useRef()

    useHotkeys(['shift', 'ctrl'], (e) => {
        const { shiftKey, ctrlKey } = e
        isShiftDown.current = shiftKey
        isCtrlDown.current = ctrlKey

        if (timer.current) {
            clearTimeout(timer.current)
        }

        // 避免摁住shift时点击输入框 导致一直为true
        timer.current = setTimeout(() => {
            isShiftDown.current = false
            isCtrlDown.current = false
            timer.current = null
        }, 10)
    }, { keyup: true, keydown: true })

    // 同步持久化配置
    useEffect(() => {
        try {
            if (item?.data_source) {
                const { comp_config } = JSON.parse(item?.data_source)
                if (!isEqual(comp_config, config)) {
                    setConfig(comp_config)
                }
            }
        } catch (error) {
            console.log('err', error)
        }
    }, [item?.data_source])

    useEffect(() => {
        setData(sampleData)
    }, [sampleData])

    // 切换组件时，如果Redux有选中的试样状态, 设为选中
    useEffect(() => {
        if (optSample) {
            setSelectedKeys([optSample.key])
            setOpt(optSample)
        }
    }, [optSample])

    const getSamples = async () => {
        try {
            const res = await getSamplesList()
            if (res) {
                dispatch({ type: PROJECT_SAMPLE_LIST, param: res })
            }
        } catch (error) {
            console.log(error)
            throw (error)
        }
    }
    const handleBachAdd = () => {
        initUnitsData()
        getSamples()
        setOpen(true)
    }

    // 获取试样数据列表
    const saveSampleTree = async (data) => {
        try {
            const res = await editSampleTreeList({ sample_tree: data })
            if (res) {
                await apiSuccess()
            }
        } catch (error) {
            console.log(error)
            throw (error)
        }
    }

    const apiSuccess = async () => {
        await initSampleTree()
        initDefaultSample()
        messageApi.open({
            type: 'success',
            content: t('操作成功')
        })
    }

    // 获取试样相关数据
    const saveSampleAbout = async (data) => {
        try {
            const res = await editSampleAbout({ instance_about_List: data })
            if (res) {
                initSampleAboutList()
            }
        } catch (error) {
            console.log(error)
            throw (error)
        }
    }

    const handleData = (data, key, func) => {
        return data.map(i => {
            if (i.key === key) {
                func(i)
            } else if (i.children ?? false) {
                i.children = handleData(i.children, key, func)
            }
            return i
        })
    }

    const handleDelData = (data, key) => {
        return data.filter(i => {
            if (i.key === key) {
                return false
            }
            if (i.children ?? false) {
                i.children = handleDelData(i.children, key)
            }
            return true
        })
    }
    const handleOpt = async (val, multi) => {
        let selectedSample = val

        // 选中试样时
        let selectedMultiSamples = multi

        // 选中分组时
        if (!isSample(selectedSample)) {
            if (isShiftDown.current && selectedSample?.key !== opt?.key) {
                // 按住shift时，并且不是取消选中
                selectedMultiSamples = [...new Set([...multi, ...selectedSample?.children?.map(m => m.key)])]
            } else {
                // 没有按住shift时
                const list = multiSample?.length > 0 ? multiSample : selectedKeys
                selectedMultiSamples = [...list]
            }
        }

        if (selectedMultiSamples && selectedMultiSamples?.length > 0 && opt?.key === selectedSample?.key) {
            selectedMultiSamples = selectedMultiSamples.filter(f => f !== selectedSample.key)
            if (selectedMultiSamples && selectedMultiSamples.length > 0) {
                selectedSample = samples().find(f => f.key === selectedMultiSamples[selectedMultiSamples.length - 1])
            }
        }
        if (selectedSample) {
            await updateOptSample(isSample(selectedSample) ? selectedSample : optSample, selectedMultiSamples, true)
            setOpt(selectedSample)
            setSelectedKeys([selectedSample.key])
        }

        dispatch({
            type: PROJECT_CURRENT_SAMPLE_MULTI,
            param: selectedMultiSamples
        })
    }
    const handelMulti = (multi) => {
        dispatch({
            type: PROJECT_CURRENT_SAMPLE_MULTI,
            param: multi
        })
    }

    const onDisable = async (disabled = true, data = opt) => {
        let sampleData = []
        if (isSample(data)) {
            sampleData = [data]
        }
        if (data?.children && data?.children.length > 0) {
            sampleData = [...sampleData, ...data?.children]
        }
        if (multiSample && multiSample.length > 0) {
            sampleData = [...sampleData, ...samples().filter(f => multiSample.includes(f.key))]
        }
        const res = await batchUpdateSample(Array.from(new Map(sampleData.map(item => [item.id, item])).values()).map(m => ({ ...m, disabled })))
        if (res) {
            await apiSuccess()
        }
    }

    const onCopy = async () => {
        if (!opt) {
            return
        }

        const res = await postSampleCopy({ id: opt.id })

        if (res) {
            message.success('复制成功')
            initSampleTree()
        }
    }

    const onEdit = () => {
        setIsAdd(TYPE.UPDATE)
        setHeadOpen(true)
    }

    const onDel = async () => {
        if (opt) {
            const groupSamples = data.filter(f => [...multiSample, opt.key].includes(f.key)).flatMap(f => f.children).map(f => f.key)
            const sample = samples().filter(f => [...multiSample, opt.key].includes(f.key)).flatMap(f => f.key)
            const ids = [...new Set([...groupSamples, ...sample, opt.key, ...multiSample])]
            Modal.confirm({
                title: t('确认删除？'),
                icon: <ExclamationCircleFilled />,
                content: <div style={{ color: 'red' }}>{t('当前操作直接从数据库删除数据且无法恢复，请确认操作')}</div>,
                okText: t('确认'),
                cancelText: t('取消'),
                onOk: async () => {
                    await batchDelSample({ ids })
                    deleteVideo(ids)
                    handelMulti(multiSample.filter(f => !ids.includes(f)))
                    await apiSuccess()
                    initVideoData()
                    onClickChange('number')
                    dispatch({ type: SUB_TASK_SHORTCUT, param: { UIParams: { shortcutCode: ALL_SHORTCUT.保存 } } })
                },
                onCancel() {
                }
            })
        }
    }

    // 删除视频
    const deleteVideo = (ids = []) => {
        // 所有当前需要删除试样对应的视频列表
        const list = videoList.filter(it => ids?.includes(it.sample_id))
        toDelete(list)
    }
    const toDelete = async (list) => {
        try {
            // 批量删除逻辑：循环调用单个删除接口
            await Promise.allSettled(
                list.map((item) => {
                    return delVideo({
                        video_id: item?.video_id,
                        video_file: item?.video_file
                    })
                })
            )
            initVideoData()
        } catch (error) {
            console.log(error)
            throw error
        }
    }

    const onAdd = debounce(async (type) => {
        if (type === BUTTON_ADD_TYPE.GROUPING) {
            const res = await addSampleDefault({ group_name: `${t('分组') + (data.length + 1)}` })
            const { count } = data?.[0]
            await onHeadOk(handleCode({
                type: TYPE.CREATE, name: `试样${count + 1}`, code: `1${randomStr()}`
            }, codePrefix.SAMPLE), { ...data[0], key: res.id })
            await apiSuccess()
            onClickChange('number')
            return
        }
        // 如果只有一个分组不用选中分组
        if (type !== BUTTON_ADD_TYPE.GROUPING && data?.length === 1) {
            const { count } = data.find(f => f.key === data[0].key)
            await onHeadOk(handleCode({
                type: TYPE.CREATE, name: `试样${count + 1}`, code: `${count + 1}${randomStr()}`
            }, codePrefix.SAMPLE), data[0])
            onClickChange('number')
            return
        }
        // 如果没有选中分组，则添加到当前选中的试样的分组
        if (type !== BUTTON_ADD_TYPE.GROUPING) {
            let tempOpt = opt
            if (isSample(opt)) {
                tempOpt = { ...opt, key: opt.parent_group }
            } else if (!opt) {
                message.error(t('请选择一个分组或试样'))
                return
            }

            if (!defaultSample?.id) {
                message.error(t('未设置试样默认参数'))
                return
            }
            setIsAdd(TYPE.CREATE)
            // setHeadOpen(true)
            const { count } = data.find(f => f.key === tempOpt.key)
            await onHeadOk(handleCode({
                type: TYPE.CREATE, name: `试样${count + 1}`, code: `${count + 1}${randomStr()}`
            }, codePrefix.SAMPLE), tempOpt)
            onClickChange('number')
        }
    }, 300)

    const handleModalData = (values, length) => {
        const {
            data, sample_type, samples, code
        } = values
        return data.map((i, index) => {
            return (
                {
                    key: crypto.randomUUID(),
                    name: `试样${(length + index + 1)}`,
                    data: i,
                    samples,
                    code: code || i[0].code,
                    color: color16(),
                    sample_type,
                    disabled: false,
                    new: true

                }
            )
        })
    }

    // 高级设置（批量新增）
    const onModalOk = (values) => {
        const tempData = cloneDeep(data).map(i => {
            if (i.key === opt.key) {
                i.children = [...i.children, ...handleModalData(values, i.children.length)]
            }
            return i
        })
        saveSampleTree(tempData)
        setOpen(false)
    }

    const onHeadOk = async (values, param = opt) => {
        const { type, name, code } = values
        if (type === TYPE.CREATE) {
            const key = crypto.randomUUID()
            // 以默认试样为模板创建新试样
            const sample_instance = {
                key,
                name,
                code,
                data: defaultSample?.data || [],
                samples: defaultSample?.samples || [],
                color: '#FFFFFF',
                sample_type: defaultSample?.sample_type || '',
                new: true,
                disabled: false,
                parent_group: param.key,
                id: key
            }
            setHeadOpen(false)
            // 新建
            const res = await addSample({ sample_instance })
            if (res) {
                await apiSuccess()
            }
        } else {
            await saveSampleTree(
                handleData(
                    cloneDeep(data),
                    values.key,
                    (e) => {
                        e.name = values.name
                    }
                ),
                code
            )
            setOpt(values)
            setHeadOpen(false)
        }
    }

    const onCancel = () => {
        setHeadOpen(false)
    }

    const onCSV = async (menuData) => {
        if (menuData?.code) {
            try {
                // 启动异步导出
                const result = await getExportCSVAsync({
                    sample_code: menuData?.code
                })

                const taskId = result?.value?.taskId || result?.value?.task_id
                if (!taskId) {
                    message.error(t('启动导出任务失败'))
                    return
                }

                const id = dispatch(addGlobalLoading('导出中...'))

                // 简单提示，显示任务ID
                message.success(
                    <div>
                        <div>{t('CSV导出任务已启动')}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                            任务ID:
                            {' '}
                            {taskId}
                        </div>
                    </div>
                )

                // 后台轮询检查完成状态（不显示进度）
                const checkCompletion = async () => {
                    try {
                        const res = await getCsvExportStatus({ task_id: taskId })
                        if (res.value?.status?.status === 'Completed') {
                            message.success(t(res.value?.status?.message))
                            dispatch(removeGlobalLoading(id))
                        } else if (res.value?.status?.status === 'Failed') {
                            message.error(t('CSV导出失败'))
                            dispatch(removeGlobalLoading(id))
                        } else {
                            // 30秒后再次检查
                            setTimeout(checkCompletion, 3000)

                            dispatch(updateGlobalLoading(id, `导出进度：${res.value?.status?.completedFiles}/${res.value?.status?.totalFiles}`))
                        }
                    } catch (error) {
                        // 静默失败，不影响用户
                        console.log('检查导出状态失败:', error)
                        dispatch(removeGlobalLoading(id))
                    }
                }

                // 30秒后开始检查
                setTimeout(checkCompletion, 3000)
            } catch (err) {
                console.error(err)
                message.error(t('启动CSV导出失败'))
            }
        }
    }

    const setTingHandle = () => {
        setSettingOpen(true)
    }
    const onClose = () => {
        setSettingOpen(false)
        // 更新持久化配置
        updateLayoutItem({
            layout: layoutConfig,
            newItem: {
                ...item,
                data_source: JSON.stringify({ comp_config: config })
            }
        })
    }

    /**
     * 根据传入的标志触发相应的事件处理函数
     *
     * @param {string} flag - 一个字符串标志，决定触发哪个事件处理函数
     *                        可以是 'checkCurrent'当前选中发生改变, 'checkNumber'按住Ctrl选中的数量发生变化, 或 'number'新增删除发生变化
     */
    const onClickChange = (flag) => {
        const { event } = config || {}
        const { checkCurrent, checkNumber, number } = event || {}
        if (flag === 'checkCurrent') {
            onEvent(checkCurrent)
        }
        if (flag === 'checkNumber') {
            onEvent(checkNumber)
        }
        if (flag === 'number') {
            onEvent(number)
        }
    }

    return (
        <SampleContent.Provider value={{ menu: menuRef }}>
            <SampleContainer>
                {contextHolder}
                <div className="sample-list">{t('试样列表')}</div>

                <SampleHead
                    handleBachAdd={handleBachAdd}
                    opt={opt}
                    onDisable={onDisable}
                    onEdit={onEdit}
                    onDel={onDel}
                    onAdd={onAdd}
                    dataSource={data}
                />
                <SampleList
                    dataSource={data}
                    handleOpt={handleOpt}
                    multiSample={multiSample}
                    onHeadOk={onHeadOk}
                    selectedData={selectedKeys}
                    onClickChange={onClickChange}
                />
                <SampleModal
                    open={open}
                    handleOpen={setOpen}
                    sampleList={sampleList}
                    unitList={unitList}
                    onOk={onModalOk}
                    init={{ ...INIT_FORM }}
                    sampleAbout={sampleAboutList}
                    saveSampleAbout={saveSampleAbout}
                />
                {
                    headOpen && (
                        <SampleHeadModal
                            open={headOpen}
                            data={opt}
                            onCancel={onCancel}
                            type={isAdd}
                            onOk={onHeadOk}
                        />
                    )
                }
                <ContextMenuRightClick
                    domId={id}
                    layoutConfig={layoutConfig}
                    onDisable={onDisable}
                    onExcel={onExcel}
                    onCSV={onCSV}
                    onCopy={onCopy}
                    setTingHandle={setTingHandle}
                />
            </SampleContainer>
            <Setting
                open={settingOpen}
                onClose={onClose}
                setConfig={setConfig}
                config={config}
            />
        </SampleContent.Provider>

    )
}

export default Sample
