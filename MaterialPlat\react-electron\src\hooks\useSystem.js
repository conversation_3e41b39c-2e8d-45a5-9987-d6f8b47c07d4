import { useDispatch } from 'react-redux'
import { getStationHostList, getSystemConfig, saveSystemConfig } from '@/utils/services'

import { UPDATE_SYS_HOME_TYPE, GLOBAL_SYSTEM_CONFIG } from '@/redux/constants/global'
import { SYS_HOME_ID_MAP } from '@/pages/sysHome/constants'
import useElectron from '@/hooks/useElectron'

import { getCurrentPath } from '@/utils/auth'
import useSysHomeLayout from './useSysHomeLayout'

export const APP_WORK_MODE = {
    硬件配置模式: 0,
    主机工作模式: 1, // 默认
    从机工作模式: 2
}

// 判断工作模式
const useSystem = () => {
    const dispatch = useDispatch()
    const { initLayoutList } = useSysHomeLayout()
    const { checkFileExists } = useElectron()

    const afterLogin = async (mode) => {
        await initLayoutList()

        const hostList = await getStationHostList()

        // 1. 判断工作模式 进入 站首页 (加载硬件站数据)
        if (
            mode === APP_WORK_MODE.硬件配置模式
            || hostList.length === 0
        ) {
            dispatch({ type: UPDATE_SYS_HOME_TYPE, param: SYS_HOME_ID_MAP.硬件配置首页 })
            return
        }

        // 2. 判断主机数量 进入 单机/多机 首页
        if (hostList.length === 1) {
            dispatch({ type: UPDATE_SYS_HOME_TYPE, param: SYS_HOME_ID_MAP.单站首页 })
            return
        }

        dispatch({ type: UPDATE_SYS_HOME_TYPE, param: SYS_HOME_ID_MAP.多站首页 })
    }

    // 获取系统配置
    const initSystemConfig = async (checkPatch = false) => {
        try {
            const res = await getSystemConfig()
            res.project_directory = res.project_directory.replace(/\\/g, '/')
            if (res) {
                if (checkPatch) {
                    const exist = await checkFileExists({ filePath: res.project_directory })

                    if (!exist) {
                        updateSystemConfig({ project_directory: `${getCurrentPath()}\\db` })

                        return
                    }
                }
                dispatch({ type: GLOBAL_SYSTEM_CONFIG, param: res })
            }
        } catch (error) {
            console.error(error)
        }
    }

    // 获取系统配置
    const updateSystemConfig = async (config) => {
        try {
            await saveSystemConfig(config)
            initSystemConfig()
        } catch (error) {
            console.error(error)
        }
    }

    return {
        afterLogin,
        initSystemConfig,
        updateSystemConfig
    }
}

export default useSystem
