import React from 'react'
import { Space } from 'antd'

export const TABS = {
    输入变量: 'input',
    信号变量: 'signal',
    结果变量: 'result',
    动作: 'action'
}

export const getColumns = ({ onCorrelation, onDel, t }) => ([
    {
        title: t('序号'),
        dataIndex: 'name',
        key: 'name',
        render: (value, record, index) => {
            return index + 1
        }
    },
    {
        title: t('全局变量'),
        render: (_, record) => [record.global_name, record.global_code].join(' - '),
        onCell: (record, index) => {
            return {
                rowSpan: record.rowSpan
            }
        }
    },
    {
        title: t('模板/项目'),
        dataIndex: 'template_name',
        key: 'template_name',
        render: (_, record) => record.template_name || record.project_name
    },
    {
        title: t('变量'),
        render: (_, record) => [record.code, record.name].join(' - ')
    },
    {
        title: t('操作'),
        render: (_, record) => {
            return (
                <Space>
                    <a onClick={() => onCorrelation(record)}>{t('关联')}</a>
                    {
                        record.code && <a onClick={() => onDel(record.id)}>{t('取消关联')}</a>
                    }
                </Space>
            )
        }
    }
])
