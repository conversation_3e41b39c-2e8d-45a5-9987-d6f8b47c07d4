using System.Diagnostics;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Text.Json;
using System.Collections.Generic;
using Buffers;
using Consts;
using MQ;
using NPOI.SS.Formula.Functions;
using ScriptEngine.InputVar;
using ScriptEngine.InputVar.InputVars;
using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;
using ScriptEngine.InstantiatedTemplate.SignalVar;
using Scripting;
using SubTaskUtils;
using static Logging.CCSSLogger;
using static Scripting.ITemplate;
using SignalVar = ScriptEngine.InstantiatedTemplate.SignalVar.SignalVar;
using Microsoft.Extensions.ObjectPool;
using System.Collections.Concurrent;

namespace SubTasks.tasks.gatherDataTask;

/**
处理按照周期采集的信号变量
*/

public class SubTaskArrayDataHandler : ISubTask
{
    public MQSubTaskSub[] subs { get; set; }
    public bool Sub_TASK_MGR_CMD { get; set; } = true;
    public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
    public bool Sub_TASK_HARDWARE_CMD { get; set; } = true;
    public bool Sub_TASK_HARDWARE_DATA { get; set; } = true;
    public bool Sub_TOPIC_FROM_UI { get; set; } = true;
    public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = true;
    public bool Sub_SelfTopic { get; set; } = false;
    public bool Sub_TOPIC_NOTIFY { get; set; } = true;

    private IObservable<Dictionary<string, double[]>>? source;
    
    private IDisposable? subscriber;
    private string? _processID;
    private string? _subtaskID;
    private string? _templateName;
    private string? _sample_Instance_Code;
    private string? _hwKey;
    private NonRepeatCircularBuffer<Dictionary<string, double>>? _circularBufferEx;
    private NonRepeatCircularBuffer<Dictionary<string, double>>? _circularBufferEx2;
    //是否使用第二个环形缓存
    //private bool ifUse_circularBufferEx2AddData = false;
    private static readonly ObjectPool<Dictionary<string, double>> _dictPool =
        new DefaultObjectPoolProvider().Create<Dictionary<string, double>>();
    private const int _maxExecPerSecond = 10;
    private int _cyclesPerExec = 1;
    private int _maxCyclesPerExec = 5;
    private int _cycleCounter = 0;
    private readonly List<Dictionary<string, double[]>> cycleBatch1 = new();
    private readonly List<Dictionary<string, double[]>> cycleBatch2 = new();
    private string? _samplingMethod;
    private double? _samplingFrequency;
    private int _intervalPoints;
    private double _circleCmdFrequency = -1.00;
    int _deviceid = 0;
    /// <summary>
    /// 是否需要间隔采集，表示是否在采集过程中跳过某些周期
    /// </summary>
    private bool _isIntervalCollected;
    double _intervalCollectedCycles=0;

    //TODO 这个目前是定死的，子任务目前不清楚哪个信号变量是频率
    private string _frequencyCode = "signal_pinlv";
    private  string _cyclesSignalCode = "signal_cycle";

    //TODO 目前在信号变量的机制，不符合客户要求的选择轴且直接关联轴下Cycle,Command等信息
    private int _daqRate;
    private bool ISRunning = false;
    SubTaskCmdParams subTaskCmdParams;
    object LockObject=new object();
    private int _bufferSize;
    ITemplate templateInst;
    void SetCode()
    { //轴下
        var ZXSignals = templateInst.SignalVars.Where(x => x.Value.DeviceId == _deviceid && x.Value.SignalValueType == null).Select(x => x.Value).FirstOrDefault();
        if (ZXSignals != null && ZXSignals.DaqRate > 0)
        {
            _daqRate = ZXSignals.DaqRate;
        }
        else
        {
            _daqRate = 10000;
        }
        //轴上信号变量
        var Signals = templateInst.SignalVars.Where(x => x.Value.DeviceId == _deviceid && x.Value.SignalValueType != null).Select(x => x.Value).ToList();
        foreach (var signal in Signals)
        {
            switch (signal.RelatedInfo)
            {
                //case "Cycles":
                   
                //    _cyclesSignalCode = signal.Code;
                //    break;
                case "CmdFrequency":
                    _frequencyCode = signal.Code;
                    break;

                default:
                    break;
            }
        }
    }

    /// <summary>
    /// 根据不同的条件来换算间隔N个点采集一个
    /// 需要确保每个周期最多采集500个点
    /// </summary>
    /// <returns></returns>
    private void GetInterval(SubTaskCmdParams t)
    {
        //采集方式和参数
        _samplingMethod =UtilsForSubTasks.ReadVarValue<string>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_sampling_type")
        );
        //采样率
        _samplingFrequency = UtilsForSubTasks.ReadVarValue<double>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_sampling_rate")
        );
        if (_samplingMethod == "TimeInterval")
        {
            _intervalPoints = (int)(_daqRate * _samplingFrequency);
        }
        else
        {
            if (_samplingFrequency <= 0) _samplingFrequency = 1;
            //间隔周期点数
            _intervalPoints = (int)(_daqRate / _samplingFrequency);
        }
        //每个周期包含多少个点
        //非0判断是否为0，防止除数为0
        if (_intervalPoints <= 0) _intervalPoints = 1;
        if (_circleCmdFrequency <= 0) _circleCmdFrequency = 1;
        //每个周期采集点数
        var countPerCircle = _daqRate / _circleCmdFrequency;
        
        Logger.Error($"周期产生信号变量用户输入采样率_samplingFrequency{_samplingFrequency}计算每个周期点数countPerCircle:{countPerCircle}采集间隔点_intervalPoints： {_intervalPoints}");
    }

    // 预编译脚本类
    private dynamic? _precompileClass;
    //校验脚本
    private readonly CSharpScriptExecution ScriptExecutor = new() { SaveGeneratedCode = true };
    private bool _evalScript(List<Dictionary<string, double[]>> data)
    {
        try
        {
            var result = (bool)ScriptExecutor.InvokeMethod(_precompileClass, "_func_eventTriggered", templateInst!, data);
            // bool result = ScriptExecutor.ExecuteCode<bool, ITemplate>(script, _template!);
            if (ScriptExecutor.Error)
            {
                Logger.Error(ScriptExecutor.Error);
                Logger.Error("周期产生信号变量脚本异常" + ScriptExecutor.ErrorMessage);
                Logger.Error(ScriptExecutor.GeneratedClassCodeWithLineNumbers);
            }
            return result;
        }
        catch (Exception ex)
        {
            Logger.Error($"子任务ID{_subtaskID}\n周期产生信号变量子任务校验脚本异常: " + ex);
            return false;
        }

    }
  
    BufferInputVar saveCycleBuffer;
    public bool Run(SubTaskCmdParams t)
    {
        //通过参数获取模板ID、子任务ID、当前试样信息、
        _processID = t!.ProcessID!;
        _subtaskID = t!.SubTaskID!;
        _sample_Instance_Code = t.SampleName!;
        _templateName = t.ClassName!;
        subTaskCmdParams = t;
        ISystemBus.SendToUIStatusTopic(GenerateStatusUIJson(t.Cmd()!));
        Logger.Info("周期型信号变量处理子任务子任务 启动:" + t);

        _hwKey = UtilsForSubTasks.GetHwkey(t.SubTaskParams);

        _bufferSize = UtilsForSubTasks.ReadVarValue<int>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_buffersize")
        );
        //两个buffer轮流存
        _circularBufferEx = new NonRepeatCircularBuffer<Dictionary<string, double>>(_bufferSize);
        _circularBufferEx2 = new NonRepeatCircularBuffer<Dictionary<string, double>>(_bufferSize);

        templateInst = GetTemplateByName(t.ClassName!);


        var subtasksparams = t!.SubTaskParams.GetProperty("schedule");

        MappingAxis mappingAxis = templateInst!.MappingHardware.GetMappingAxis(
                    UtilsForSubTasks.ReadVarValue<object[]>(
                       t.ClassName!,
              subtasksparams.GetProperty("control_input_deviceid")
                    )
                );
        if (mappingAxis is null)
        {
            Finish(t);
            return false;
        }
        _deviceid = mappingAxis.RealIndex;
        SetCode();


        ScriptExecutor.AddDefaultReferencesAndNamespaces();
        string code = UtilsForSubTasks.ReadVarValue<string>(
                            templateInst,
                            t.SubTaskParams.GetProperty("schedule")
                                .GetProperty("control_input_strscript"));
        var method = "public bool _func_eventTriggered (ITemplate Model, List<Dictionary<string, double[]>> cycles)" +
                              Environment.NewLine +
                              "{" +
                              Environment.NewLine +
                              code +
                              Environment.NewLine +
                              "}";
        _precompileClass = ScriptExecutor.CompileClass(
            ScriptExecutor.GenerateClass(method).ToString());
        if (ScriptExecutor.Error)
        {
            // 打印和通知前端虚拟信号变量预编译报错
            Logger.Error("周期产生信号变量子任务预编译错误: " + ScriptExecutor.Error);
            Logger.Error(ScriptExecutor.ErrorMessage);
            Logger.Error(ScriptExecutor.GeneratedClassCodeWithLineNumbers);
            ISystemBus.SendToUIscriptPrecompileErrorTopic(
                "周期产生信号变量子任务预编译错误: " + ScriptExecutor.ErrorMessage
                , ScriptExecutor.GeneratedClassCodeWithLineNumbers);
        }

        _cyclesSignalCode = UtilsForSubTasks.ReadVarValue<string>(
            _templateName,
             t.SubTaskParams.GetProperty("schedule")
            .GetProperty("control_input_cyclesSignalCode")
          );
        // 采集的数据存储的buffer
        string bindBuffercode = UtilsForSubTasks.ReadVarValue<string>(
            templateInst,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_bufferCode")
        );
        BufferInputVar daqBuffer = templateInst.GetVarByName<BufferInputVar>(bindBuffercode);

        // 周期的数据存储的buffer
        string bindTargetBuffercode = UtilsForSubTasks.ReadVarValue<string>(
            templateInst,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_target_buffer")
        );
        saveCycleBuffer = templateInst.GetVarByName<BufferInputVar>(
            bindTargetBuffercode
        );
        //固定间隔周期
        _isIntervalCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_interval_specified_period"),
            "IsCheck"
        );
        _intervalCollectedCycles = UtilsForSubTasks.ReadVarValue<ulong>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_interval_specified_period")
        );
        //对数参数
        _isLogarithmicCollection = UtilsForSubTasks.ReadVarValue<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_log")
        );
        string[] daqSignalNames = daqBuffer.SignalCodes;
        List<SignalVar> templateSignalVars = new List<SignalVar>(templateInst.SignalVars.Values);
        // 根据 templateSignalVars 获取本次应该订阅哪些流
        List<string> uniqueHwKeys = templateSignalVars
            .Select(signal => signal.HwKey)
            .Distinct()
            .ToList()!;

        // foreach (var uniqueHwKey in uniqueHwKeys)
        // {
        // if (uniqueHwKey == null)
        //     continue;

        IObservable<Dictionary<string, double>> daqStream = templateInst.TemplateObservable.Where(x =>
       x.Keys.Any(key => daqSignalNames.Contains(key))
   );
        //source.SelectMany(x =>
        // {
        //     Dictionary<string, double[]> subVars = x.Where(kvp =>
        //             daqSignalNames.Contains(kvp.Key)
        //         )
        //         .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        //     var results = subVars
        //         .First()
        //         .Value.Select(
        //             (_value, index) =>
        //             {
        //                 Dictionary<string, double> daqSignalVars = new();
        //                 foreach (var item in daqSignalNames)
        //                 {
        //                     double nullAbleValue = GetArrayElementAtIndex(subVars[item], index)!;
        //                     daqSignalVars[item] = nullAbleValue;
        //                 }
        //                 return daqSignalVars;
        //             }
        //         );
        //     return results.ToObservable();
        // });

        Func<Dictionary<string, double>, long, bool> ConditionIntervalData = (signal, index) =>
            index % _intervalPoints == 0;

        Action<Dictionary<string, double>> GetCmdFrequencyFromStream = (x) =>
        {
            if (x[_frequencyCode] != _circleCmdFrequency)
            {
                _circleCmdFrequency = x[_frequencyCode];
                GetInterval(t!);
                //先计算出支持的最小间隔周期
                _cyclesPerExec = Math.Max(1, (int)Math.Ceiling(_circleCmdFrequency / _maxExecPerSecond));
                _maxCyclesPerExec = _cyclesPerExec * 5;
            }
        };
        //过滤数据源
        var filteredDataStream = daqStream
            .Where(signals => signals[_frequencyCode] > 0)
            .Do(signal => GetCmdFrequencyFromStream(signal))
            .Where((signal, Index) => ConditionIntervalData(signal, Index));

        //存储一个周期的数据，周期切换则下发周期数据
        subscriber = filteredDataStream.Subscribe(dic =>
        {
            AddCycleData(dic);
        });
        return true;
    }
    public void TestTake()
    {
        if (_unreadItems.Count > 0)
        {
            Task.Run(() =>
               {
                   lock (LockObject)
                   {
                       if (_unreadItems.Count > 0)
                       {
                           List<Dictionary<string, double[]>> cycleData = new List<Dictionary<string, double[]>>();
                           while (_unreadItems.TryDequeue(out Dictionary<string, double>[] dequeuedItem))
                           {
                               Logger.Error("正在计算周期" + dequeuedItem[0][_cyclesSignalCode]);
                               var keyArray = dequeuedItem
                                       .SelectMany(d => d)
                                       .GroupBy(kvp => kvp.Key)
                                       .ToDictionary(g => g.Key, g => g.Select(kvp => kvp.Value).ToArray());
                               cycleData.Add(keyArray);
                           }
                            var cycleData = _cycleBatch.Last();
                            {
                                saveCycleBuffer.Reset();
                                if (cycleData.Count > 0) { }
                                foreach (var item in saveCycleBuffer.Value)
                                {
                                    foreach (var value in cycleData[item.Key])
                                    {
                                        item.Value.AddValue(value);
                                    }
                                }
                            }
                           _evalScript(cycleData);
                       }
                   }
               });
        }
    }
    private readonly ConcurrentQueue<Dictionary<string, double>[]> _unreadItems = new ConcurrentQueue<Dictionary<string, double>[]>();

    //数据缓存预添加
    private void DataAddMethod(Dictionary<string, double>[] oneCycleOrignalSignalData)
    {
        //已经停止不再运行
        if (Stop == true)
        {
            return;
        }
        if (oneCycleOrignalSignalData == null || oneCycleOrignalSignalData.Length == 0)
        {
            return;
        }
        
        if (_unreadItems.Count  > 50)
        {
            Logger.Error("数据缓存满50次不再缓存，当前周期" + oneCycleOrignalSignalData[0][_cyclesSignalCode]);
            return;
        }
        else
        {
            _unreadItems.Enqueue(oneCycleOrignalSignalData);
        }
        //只是触发一下
        TestTake();
        return;
        // //如果使用buffer1
        // //buffer1添加数据
        // //如果使用buffer2
        // //buffer2添加数据
        // lock (LockObject)
        // {
        //     //var oneCycleOrignalSignalData = dic.ReadBuffer();
        //     if (useCycle == 1)
        //     {
        //         DataAddMethod(keyArray, cycleBatch1);
        //     }
        //     else
        //     {
        //         DataAddMethod(keyArray, cycleBatch2);
        //     }
        // }
    }
    //数据缓存添加
    private void DataAddMethod(Dictionary<string, double[]> keyArray, List<Dictionary<string, double[]>> cycleBatch)
    {
        if (_cycleCounter < _maxCyclesPerExec)
        {
            _cycleCounter++;
            cycleBatch.Add(keyArray);
            if (_cycleCounter > _cyclesPerExec)
            {
                Logger.Error($"数据因性能问题，未能及时处理，请检查性能瓶颈所在。周期：{keyArray[_cyclesSignalCode][0]}, 时间：{DateTime.Now}");
            }
        }
        else
        {
            Logger.Error($"内存已存储{_cycleCounter}个周期仍未完成计算不再添加，抛出周期：{keyArray[_cyclesSignalCode][0]}, 时间：{DateTime.Now}");
        }
    }
    //数据缓存处理
    private void DataProssMethod()
    {
        lock (LockObject)
        {
            //如果当前有周期数据，则触发处理
            if (_cycleCounter > 0 && ISRunning == false)
            {
                if (useCycle == 1)
                {
                    DataProssMethod(cycleBatch1);
                }
                else
                {
                    DataProssMethod(cycleBatch2);
                }
            }
        }
    }
    private void DataProssMethod(List<Dictionary<string, double[]>> cycleBatch)
    {
        if (ISRunning == false)
        {
            if (useCycle == 1)
            {
                useCycle = 2;
            }
            else
            {
                useCycle = 1;
            }
            TaskRun(cycleBatch);
            //重置周期计数器
            _cycleCounter = 0;
        }
    }

    private bool FilterCycle(double cycle)
    {
        
        if (cycle > 0)
        {
            if (cycle == dataCycle)
            {
                return true;
            }
            if (_isIntervalCollected)
            {
                if (Math.Abs(cycle % _intervalCollectedCycles) < 0.5)
                {
                    dataCycle = cycle;
                    return true;
                }
            }
            
            if (_isLogarithmicCollection)
            {

                var temp = (ulong)Math.Pow(10, Math.Floor(Math.Log10(cycle)));
                if (cycle == 0 ? false : cycle % temp == 0)
                {
                    dataCycle = cycle;
                    return true;
                }
            }
        }
        return false;
    }

    //给下面的TaskRun方法用的
    object objectLock2 = new();
    void TaskRun(List<Dictionary<string, double[]>> _cycleBatch)
    {

        Task.Run(() =>
        {
            lock (objectLock2)
            {
                if (ISRunning == false)
                {
                    ISRunning = true;
                    try
                    {
                        //saveCycleBuffer存最后一个周期的数   
                        if (_cycleBatch.Count > 0)
                        {
                            var cycleData = _cycleBatch.Last();
                            {
                                saveCycleBuffer.Reset();
                                if (cycleData.Count > 0) { }
                                foreach (var item in saveCycleBuffer.Value)
                                {
                                    foreach (var value in cycleData[item.Key])
                                    {
                                        item.Value.AddValue(value);
                                    }
                                }
                            }
                        }

                        _evalScript(_cycleBatch);
                        _cycleBatch.Clear();
                        ISRunning = false;
                    }
                    catch (Exception ex)
                    {
                        ISRunning = false;
                        Logger.Error($"子任务ID{_subtaskID}\n周期产生信号变量周期数据处理异常: {ex}");
                    }
                }
            }
        });
    }
    private int useCycle = 1;
   
    private double dataCycle = 0;
    private void AddCycleData(Dictionary<string, double> dic)
    {
        //已经停止不再运行
        if (Stop == true)
        {
            return;
        }

        var currentCycle = dic[_cyclesSignalCode];
        //不是上次采集周期
        if (currentCycle != dataCycle)
        {
            var data = _circularBufferEx.ReadBuffer();
            //有数据则下发，没数据则触发计算
            if (data != null && data.Length > 0)
            {
                DataAddMethod(data);
            }
            // if (_cycleCounter > 0 && ISRunning == false)
            // {
            //     DataProssMethod();
            // }
        }
        // var pooledDic = _dictPool.Get();
        // pooledDic.Clear();
        // foreach (var kvp in dic)
        // {
        //     pooledDic[kvp.Key] = kvp.Value;
        // }
        //添加数据
        if (FilterCycle(currentCycle))
        {
            _circularBufferEx.AddValue(dic);
        }
    }

    private class UICmdParam
    {
        public List<Variable_Circle>? VarValues { get; set; }
    }
    bool Stop=false;
    private bool _isLogarithmicCollection =false;

    public void StopTask()
    {
        var data = _circularBufferEx.ReadBuffer();
        //有数据则下发，没数据则触发计算
        if (data != null && data.Length > 0)
        {
            DataAddMethod(data);
        }
        Stop = true;
        return;
        if (_circularBufferEx == null) return;
        //拿到现在存的数据
        var keyArray = _circularBufferEx.ReadBuffer()
                  .SelectMany(d => d)
                  .GroupBy(kvp => kvp.Key)
                  .ToDictionary(g => g.Key, g => g.Select(kvp => kvp.Value).ToArray());
        if (keyArray.Count > 0)
        {
            //等待添加逻辑处理完
            lock (LockObject)
            {

                //var oneCycleOrignalSignalData = dic.ReadBuffer();

                _cycleCounter++;
                if (useCycle == 1)
                {
                    cycleBatch1.Add(keyArray);
                    //等待任务处理write锁释放
                    lock (objectLock2)
                    {
                        TaskRun(cycleBatch1);
                    }
                }
                else
                {
                    cycleBatch2.Add(keyArray);
                    //等待任务处理write锁释放
                    lock (objectLock2)
                    {
                        TaskRun(cycleBatch2);
                    }
                }
            }
        }
    }
    
    public bool Abort(SubTaskCmdParams Params)
    {
        StopTask();
        ISystemBus.SendToUIStatusTopic(GenerateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD));
        
        if (subscriber != null)
            subscriber.Dispose();
        Logger.Info("周期型信号变量处理子任务 终止:" + Params);
        ISystemBus.SendToTaskUpTopic(
            CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!)
        );
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Finish(SubTaskCmdParams Params)
    {
        StopTask();
        ISystemBus.SendToUIStatusTopic(GenerateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD));
        if (subscriber != null)
            subscriber.Dispose();
        Logger.Info("周期型信号变量处理子任务 终止:" + Params);
        ISystemBus.SendToTaskUpTopic(
            CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!)
        );
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Finish()
    {
        StopTask();
        if (null != subscriber)
            subscriber.Dispose();
        
        //通知UI子任务结束
        ISystemBus.SendToUIStatusTopic(
            UtilsForSubTasks.GenerateStatusUIJson(
                CmdConsts.RCV_FINISH_TASK_CMD,
                _processID!,
                _subtaskID!
            )
        );
        Logger.Info(
            "**周期型信号变量处理子任务周期波子任务** 完成:"
                + UtilsForSubTasks.GenerateStatusUIJson(
                    CmdConsts.RCV_FINISH_TASK_CMD,
                    _processID!,
                    _subtaskID!
                )
        );
        //通知多任务管理器，子任务结束
        ISystemBus.SendToTaskUpTopic(
            CmdConsts.SubTaskFinishCmd(_templateName, _processID, _subtaskID)
        );
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public string[] GetSelfTopic()
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromScript(string ParametersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromUI(string ParametersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromVAR(string topic, string ParametersString)
    {
        throw new NotImplementedException();
    }

    public void ImportHwFuncRet(string ParametersString)
    {
        throw new NotImplementedException();
    }

    public SubTaskCmdParams? ImportParams(string ParametersString)
    {
        Logger.Info("ImportPrams" + " :" + ParametersString);
        var x = JsonSerializer.Deserialize<SubTaskCmdParams>(ParametersString);

        return x!;
    }

    public bool Pause(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(
            UtilsForSubTasks.GenerateStatusUIJson(
                CmdConsts.RCV_PAUSE_TASK_CMD,
                _processID!,
                _subtaskID!
            )
        );
        Logger.Info("周期型信号变量处理子任务暂停了");
        return true;
    }

    public bool Resume(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(
            UtilsForSubTasks.GenerateStatusUIJson(
                CmdConsts.RCV_RESUME_TASK_CMD,
                _processID!,
                _subtaskID!
            )
        );
        return true;
    }

    public bool ProcessData(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    //private record CycleSignal(string Name, ArrayCycle ArrayCycle);
    private record ArrayCycle(ulong Cycle, List<double> Points);

    public JsonElement UIParams()
    {
        throw new NotImplementedException();
    }

    private static double GetArrayElementAtIndex(double[] array, int index)
    {
        if (index >= 0 && index < array.Length)
        {
            return array[index];
        }
        else
        {
            return default;
        }
    }

    //组装发送给前端关于子任务状态的参数
    public string GenerateStatusUIJson(string status_str)
    {
        var uicmdParam = new UIStatusParam(status_str);
        // 构建 JSON 字符串
        var jsonStr = JsonSerializer.Serialize(uicmdParam);

        // 通过解析 JSON 字符串创建 JsonDocument 对象
        using JsonDocument document = JsonDocument.Parse(jsonStr);
        // 获取根节点的 JsonElement
        JsonElement root = document.RootElement;

        // 构建 UICmdParams 对象，并将 UIParams 设置为 JsonElement
        var uicmdParams = new UICmdParams(_processID!, _subtaskID!, "taskStatus", root);

        return JsonSerializer.Serialize(uicmdParams);
    }

    public bool ReStart(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public bool Error(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public void HandleNotify(string notifyTitle, string msg)
    {
        if (notifyTitle == "UpdateParam" &&
           msg == _templateName! + "-" + _subtaskID!)
        {
            _isIntervalCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
               _templateName,
               subTaskCmdParams.SubTaskParams.GetProperty("schedule")
                   .GetProperty("control_input_interval_specified_period"),
               "IsCheck"
           );
            _intervalCollectedCycles = UtilsForSubTasks.ReadVarValue<ulong>(
                    _templateName,
                    subTaskCmdParams.SubTaskParams.GetProperty("schedule")
                        .GetProperty("control_input_interval_specified_period")
            );
        }
        
    }
}
