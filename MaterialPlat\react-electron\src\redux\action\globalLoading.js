import { GLOBAL_LOADING_LIST } from '../constants/global'

// 生成唯一id的工具函数
const generateLoadingId = (prefix = 'loading') => {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 添加新的加载项到全局loading数组
 * @param {string} content - 显示在loading中的内容
 * @param {string} prefix - id前缀，可选
 * @returns {string} 生成的唯一id
 */
const addGlobalLoading = (content, prefix = 'loading') => {
    return (dispatch, getState) => {
        const { globalLoading } = getState().global

        // 自动生成唯一id
        const id = generateLoadingId(prefix)

        const loadingItem = {
            id,
            content
        }

        // 直接添加新项，因为id是唯一生成的
        const newGlobalLoading = [...globalLoading, loadingItem]

        dispatch({
            type: GLOBAL_LOADING_LIST,
            param: newGlobalLoading
        })

        // 返回生成的id，方便后续移除
        return id
    }
}

/**
 * 从全局loading数组中移除指定的加载项
 * @param {string} loadingId - 要移除的加载项id
 */
const removeGlobalLoading = (loadingId) => {
    return (dispatch, getState) => {
        const { globalLoading } = getState().global

        // 过滤掉指定id的加载项
        const newGlobalLoading = globalLoading.filter(item => item.id !== loadingId)

        dispatch({
            type: GLOBAL_LOADING_LIST,
            param: newGlobalLoading
        })
    }
}

/**
 * 更新指定id的全局loading显示内容
 * @param {string} loadingId - 要更新的加载项id
 * @param {string} content - 新的显示内容
 */
const updateGlobalLoading = (loadingId, content) => {
    return (dispatch, getState) => {
        const { globalLoading } = getState().global

        // 更新指定id的加载项内容
        const newGlobalLoading = globalLoading.map(item => (item.id === loadingId ? { ...item, content } : item))

        dispatch({
            type: GLOBAL_LOADING_LIST,
            param: newGlobalLoading
        })
    }
}

/**
 * 清空所有全局loading项
 */
const clearGlobalLoading = () => {
    return {
        type: GLOBAL_LOADING_LIST,
        param: []
    }
}

export {
    addGlobalLoading,
    removeGlobalLoading,
    updateGlobalLoading,
    clearGlobalLoading
}
