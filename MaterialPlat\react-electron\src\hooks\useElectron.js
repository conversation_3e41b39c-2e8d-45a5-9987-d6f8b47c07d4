import React from 'react'
import { useTranslation } from 'react-i18next'

import { ELECTRON_TITLE } from '@/utils/constants'

const { ipc<PERSON>ender<PERSON> } = process.env.REACT_APP_IS_BROWSER !== 'true' ? window.require('electron') : {}

// 其余窗口数量
let subWindowCount = 0

if (process.env.REACT_APP_IS_BROWSER !== 'true') {
    ipcRenderer.on('subWindowClose', () => {
        subWindowCount -= 1
    })
}

const useElectron = () => {
    const { t } = useTranslation()
    // 发送更新标题的消息到主进程
    const updateTitle = (title = t(ELECTRON_TITLE)) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.send('update-title', title)
        }
    }

    // 发送更新窗口
    const updateWindowSize = (param = { width: 1920, height: 1080 }) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.send('update-window-size', param)
        }
    }

    // 发送更新窗口最大化
    const windowMaximize = (isFullScreenable = false) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.send('maximize', isFullScreenable)
        }
    }

    // 打开选择文件夹路径
    const openDialog = (type = 'open-dialog', param) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            const filePath = ipcRenderer.sendSync(type, param)
            return filePath
        }
        return ''
    }

    // 发送关闭进程消息
    const sendChildProcess = (title = ELECTRON_TITLE) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.send('child-process-cmd', { cmd: 'remove-childs' })
        }
    }

    // 写入文件
    const saveWriteFile = (param) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.send('save-write-file', param)
        }
    }

    // 读取文件
    const getReadFile = (param) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.send('read-file', param)
        }
    }

    // 读取log文件
    // 目前主要读取2个一个是taskServer.log , hardware.log
    const getReadLog = async (param) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.invoke('read-log', param)
        }
        return null
    }

    // 保存视频
    const saveVideoFile = (param) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.invoke('save-video', param)
        }
        return null
    }

    // 读取文件
    const getRead = async (param) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.invoke('read', param)
        }
        return null
    }

    // 监听主进程消息
    const onListenIPC = (type, fn) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.on(type, fn)
        }
    }

    // 销毁监听主进程消息
    const offListenIPC = (type, fn) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            ipcRenderer.off(type, fn)
        }
    }

    // 读取app配置文件
    const readAppConfig = async ({ key } = {}) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.invoke('readAppConfig', { key })
        }
        return null
    }

    // 判断路径是否存在
    const checkFileExists = async ({ filePath } = {}) => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.invoke('checkFileExists', { filePath })
        }
        return null
    }

    const startTaskServer = async () => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.send('startTaskServer')
        }
        return null
    }

    const killTaskServer = async () => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.invoke('killTaskServer')
        }
        return null
    }

    // 打开项目分屏
    const openProjectSubWindow = async ({
        projectId, pageId, width, height
    }) => {
        const url = `/dialog/${projectId}/${pageId}`
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            subWindowCount += 1
            return ipcRenderer.send('openWindow', { url, width, height })
        }
        return null
    }

    const sendMsgToSubWindow = async ({ subTopic, data }) => {
        // 没有子窗口不再发消息
        if (subWindowCount === 0) {
            return null
        }

        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.send('sendMsgToSubWindow', { subTopic, data })
        }
        return null
    }

    const showSubWindow = async () => {
        if (process.env.REACT_APP_IS_BROWSER !== 'true') {
            return ipcRenderer.send('showSubWindow')
        }
        return null
    }

    return {
        updateTitle,
        openDialog,
        sendChildProcess,
        saveWriteFile,
        onListenIPC,
        getReadFile,
        offListenIPC,
        getReadLog,
        getRead,
        saveVideoFile,
        readAppConfig,
        updateWindowSize,
        windowMaximize,
        startTaskServer,
        killTaskServer,
        openProjectSubWindow,
        sendMsgToSubWindow,
        showSubWindow,
        checkFileExists
    }
}

export default useElectron
