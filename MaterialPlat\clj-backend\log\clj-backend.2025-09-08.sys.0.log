2025-09-08 09:16:26,242 [main] INFO  clj-backend.core - -main running ~~ 
2025-09-08 09:16:26,576 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-09-08 09:16:27,655 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-09-08 09:16:28,246 [main] INFO  luminus.http-server - server started on port 3000 
2025-09-08 09:16:28,246 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-09-08 09:16:28,270 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-09-08 09:16:28,272 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-09-08 09:16:28,272 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-09-08 09:16:28,273 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-09-08 09:16:28,273 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-09-08 09:16:28,273 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-09-08 09:16:28,274 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-09-08 09:16:28,274 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-09-08 09:16:28,275 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-09-08 09:16:28,275 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-09-08 09:16:28,275 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-09-08 09:16:28,275 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-09-08 09:16:28,276 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-09-08 09:16:28,276 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-09-08 09:16:28,276 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-09-08 09:16:28,276 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
2025-09-08 10:27:08,826 [qtp1566353334-206] INFO  clj-backend.env - 
-=[clj-backend has shut down successfully]=- 
2025-09-08 10:27:08,826 [qtp1566353334-206] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-09-08 18:04:46,961 [main] INFO  clj-backend.core - -main running ~~ 
2025-09-08 18:04:47,276 [main] INFO  clj-backend.env - 
-=[clj-backend started successfully using the development profile]=- 
2025-09-08 18:04:48,281 [main] INFO  clj-backend.core - Starting monitoring server on port 8181 
2025-09-08 18:04:48,909 [main] INFO  luminus.http-server - server started on port 3000 
2025-09-08 18:04:48,910 [main] INFO  clj-backend.nrepl - starting nREPL server on port 7000 
2025-09-08 18:04:48,931 [main] INFO  clj-backend.core - #'clj-backend.db.connections/*db-connections started 
2025-09-08 18:04:48,931 [main] INFO  clj-backend.core - #'clj-backend.common.template-utils/connect-template started 
2025-09-08 18:04:48,931 [main] INFO  clj-backend.core - #'clj-backend.events/project-life-cycle-channel started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-scheduler.mq/schedulers started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.modules.script.service/precompile-scripts started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.common.trial/end-time-time-timestamp started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.modules.project.project-service/project-event-listener started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/generate-inspection-record started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.modules.inspection.inspection-record-routes/notify-inspect started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.handler/init-app started 
2025-09-08 18:04:48,932 [main] INFO  clj-backend.core - #'clj-backend.handler/app-routes started 
2025-09-08 18:04:48,933 [main] INFO  clj-backend.core - #'clj-scheduler.core/scheduler-socket-server started 
2025-09-08 18:04:48,933 [main] INFO  clj-backend.core - #'clj-backend.core/monitoring-server started 
2025-09-08 18:04:48,933 [main] INFO  clj-backend.core - #'clj-backend.core/http-server started 
2025-09-08 18:04:48,933 [main] INFO  clj-backend.core - #'clj-backend.core/repl-server started 
