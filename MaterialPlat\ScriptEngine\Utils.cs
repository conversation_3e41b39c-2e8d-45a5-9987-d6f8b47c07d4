using Consts;
using Dapper;
using FuncLibs;
using MQ;
using NPOI.SS.Formula.Functions;
using ScriptEngine;
using ScriptEngine.DaqHandlers;
using ScriptEngine.InputVar.InputVars;
using ScriptEngine.InstantiatedTemplate.SignalVar;
using System.Globalization;
using System.Text;
using System.Text.Json;
using System.Xml.Linq;
using static ScriptEngine.CreepDaqHandler;

namespace Scripting;

/// <summary>
/// 脚本库工具类
/// </summary>
internal static class Utils
{

    /// <summary>
    /// 将JsonElement转换为合适的.NET类型
    /// </summary>
    /// <param name="jsonElement">要转换的JsonElement</param>
    /// <returns>转换后的值</returns>
    public static object ConvertJsonElement(JsonElement jsonElement)
    {
        try
        {
            return jsonElement.ValueKind switch
            {
                JsonValueKind.Number =>
                    jsonElement.TryGetInt64(out var intVal) ? intVal : jsonElement.GetDouble(),
                JsonValueKind.String => jsonElement.GetString() ?? string.Empty,
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null,
                _ => jsonElement
            };
        }
        catch (Exception ex)
        {
            Logger.Error($"JsonElement转换失败: {ex.Message}");
            return jsonElement;
        }
    }
    /// <summary>
    /// 获取可使用的文件路径-无变量引用，并且其他类有使用，转为静态方法
    /// </summary>
    /// <param name="Path"></param>
    /// <param name="FileName"></param>
    /// <param name="FileType"></param>
    /// <returns></returns>
    public static string GetFilePATH(string Path, string FileName, string FileType)
    {
        Directory.CreateDirectory(Path);
        string filePATH = Path + FileName + FileType;
        // 检查文件是否被占用，如果被占用则创建副本
        int copyIndex = 1;
        while (IsFileLocked(filePATH))
        {
            filePATH = Path + FileName + $"_副本{copyIndex}" + FileType;
            copyIndex++;
        }
        return filePATH;
    }
    /// <summary>
    /// 检查文件是否被锁定
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>如果文件被锁定返回true，否则返回false</returns>
    private static bool IsFileLocked(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return false;

            using (FileStream stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.None))
            {
                stream.Close();
            }
        }
        catch (IOException)
        {
            // 文件被占用
            return true;
        }
        catch (Exception)
        {
            // 其他异常
            return true;
        }

        return false;
    }
    /// <summary>
    /// 生成不重复ID
    /// </summary>
    internal static string GenerateUniqueId()
    {
        const int stringSize = 8;

        string str = "abcdefghijkmnopqrstuvwxyz1234567890";
        StringBuilder stringBuilder = new(stringSize);
        int num1 = 0;
        foreach (byte num2 in Guid.NewGuid().ToByteArray())
        {
            stringBuilder.Append(str[(int)num2 % str.Length]);
            ++num1;
            if (num1 >= stringSize)
                break;
        }
        return stringBuilder.ToString();
    }

    /// <summary>
    /// 获得带有代码行数的文本
    /// </summary>
    internal static string GetTextWithLineNumbers(string text, string lineFormat = "{0}.  {1}")
    {
        if (string.IsNullOrEmpty(text))
            return text;
        StringBuilder stringBuilder = new();
        string[] lines = GetLines(text);
        int totalWidth = 2;
        if (lines.Length > 9999)
            totalWidth = 5;
        else if (lines.Length > 999)
            totalWidth = 4;
        else if (lines.Length > 99)
            totalWidth = 3;
        else if (lines.Length < 10)
            totalWidth = 1;
        lineFormat += "\r\n";
        for (int index = 1; index <= lines.Length; ++index)
        {
            string str = index.ToString().PadLeft(totalWidth, ' ');
            stringBuilder.AppendFormat(lineFormat, (object)str, (object)lines[index - 1]);
        }
        return stringBuilder.ToString();
    }

    internal static string[] GetLines(string s, int maxLines = 0)
    {
        if (s == null)
            return (string[])null;
        s = s.Replace("\r\n", "\n");
        if (maxLines < 1)
            return s.Split('\n');
        return ((IEnumerable<string>)s.Split('\n')).Take(maxLines).ToArray();
    }
  
}


/// <summary>
/// 在脚本里使用的工具类
/// </summary>
public static class ScriptUtils
{
    /// <summary>
    /// 获取本地时间字符串
    /// </summary>
    /// <returns></returns>
    public static string GetLocalTimeString()
    {
        return DateTime.Now.ToString(
            "yyyy-MM-dd HH:mm:ss",
            CultureInfo.InvariantCulture
        );
    }


    /// <summary>
    /// 发送“静态曲线重新开始实验”的RESTART消息
    /// ITemplate _template=Model,;
    /// string BufferCode="input_zlc";
    /// Scripting.ScriptUtils.RESTART(Model,BufferCode)
    /// </summary>
    /// <param name="_template"></param>
    /// <param name="BufferCode"></param>
    public static void RESTART(this ITemplate _template, string BufferCode)
    {
        // 定义参数对象
        var parameters = new
        {
            SampleInstCode = _template.CurrentInst.Code,
            BufferCode = BufferCode,
            BufferMode = "RESTART"
        };
        // 直接序列化为 JsonElement
        JsonElement uiParams = JsonSerializer.SerializeToElement(parameters);
        var uiCmd = new UICmdParams(
            _template.TemplateName,
            "fakeSubtask",
            "BufferReset",
            uiParams
        );
        MQ.ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(uiCmd));
    }
    /// <summary>
    /// 清前端曲线
    /// ITemplate _template=Model,;
    /// string BufferCode="input_zlc";
    /// Scripting.ScriptUtils.RESTARTCurve(Model,BufferCode)
    /// </summary>
    /// <param name="_template"></param>
    /// <param name="BufferCode"></param>
    public static void RESTART_Curve(this ITemplate _template, string BufferCode)
    {
        // 定义参数对象
        var parameters = new
        {
            SampleInstCode = _template.CurrentInst.Code,
            BufferCode = BufferCode,
            BufferMode = "RESTART_Curve"
        };
        // 直接序列化为 JsonElement
        JsonElement uiParams = JsonSerializer.SerializeToElement(parameters);
        var uiCmd = new UICmdParams(
            _template.TemplateName,
            "fakeSubtask",
            "BufferReset",
            uiParams
        );
        MQ.ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(uiCmd));
    }
    /// <summary>
    /// 清前端缓存
    /// ITemplate _template=Model,;
    /// string BufferCode="input_zlc";
    /// Scripting.ScriptUtils.RESTART_Cache(Model,BufferCode)
    /// </summary>
    /// <param name="_template"></param>
    /// <param name="BufferCode"></param>
    public static void RESTART_Cache(this ITemplate _template, string BufferCode)
    {
        // 定义参数对象
        var parameters = new
        {
            SampleInstCode = _template.CurrentInst.Code,
            BufferCode = BufferCode,
            BufferMode = "RESTART_Cache"
        };
        // 直接序列化为 JsonElement
        JsonElement uiParams = JsonSerializer.SerializeToElement(parameters);
        var uiCmd = new UICmdParams(
            _template.TemplateName,
            "fakeSubtask",
            "BufferReset",
            uiParams
        );
        MQ.ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(uiCmd));
    }


    /// <summary>
    /// 清空指定试样+Buffer的数据库数据
    /// ITemplate _template=Model,;
    /// string BufferCode="input_zlc";
    /// Scripting.ScriptUtils.REMOVE(Model,BufferCode)
    /// </summary>
    /// <param name="_template"></param>
    /// <param name="BufferCode"></param>
    public static void REMOVE(this ITemplate _template, string BufferCode)
    {
        _template.Db!.RestartBuffer(_template.CurrentInst.Code, BufferCode);
    }

    /// <summary>
    /// 生成正弦波曲线数据
    /// </summary>
    /// <param name="zenith">最高点值</param>
    /// <param name="nadir">最低点值</param>
    /// <param name="frequency">频率</param>
    /// <returns>返回：X轴数据、Y轴数据</returns>
    public static Dictionary<string, double[]> GenerateSineWaveData(double zenith, double nadir, double frequency)
    {
        Dictionary<string, double[]> data = new();
        // 参数设置
        double amplitude = (zenith - nadir) / 2;      // 振幅
        double phase = 0;                             // 相位
        double verticalOffset = (zenith + nadir) / 2; // 纵向偏移
        double xStart = 0;                            // X轴起始值
        double xEnd = 2 * Math.PI;                    // X轴结束值
        int numberOfPoints = 1000;                    // 数据点数量

        // 计算步长
        double stepSize = (xEnd - xStart) / (numberOfPoints - 1);

        // 生成X轴数据
        double[] xData = new double[numberOfPoints];
        for (int i = 0; i < numberOfPoints; i++)
        {
            xData[i] = xStart + i * stepSize;
        }

        // 生成Y轴数据
        double[] yData = new double[numberOfPoints];
        for (int i = 0; i < numberOfPoints; i++)
        {
            yData[i] = amplitude * Math.Sin(frequency * xData[i] + phase) + verticalOffset;
        }

        data.Add("X", xData);
        data.Add("Y", yData);

        return data;
    }

    /// <summary>
    /// 生成拟合曲线数据
    /// </summary>
    /// <param name="slope">斜率</param>
    /// <param name="y0">y轴起始点</param>
    /// <returns>返回：X轴数据、Y轴数据</returns>
    public static Dictionary<string, double[]> GenerateFittedCurveData(double slope, double y0)
    {
        Dictionary<string, double[]> data = new();

        int numberOfPoints = 10; // 生成的点数 
        double step = 1.0;       // 步长 
        double x0 = 0.0;         // 起始点的x坐标

        double[] xData = new double[numberOfPoints];
        double[] yData = new double[numberOfPoints];

        for (int i = 0; i < numberOfPoints; i++)
        {
            double x = x0 + i * step;
            xData[i] = x;
            yData[i] = slope * (x - x0) + y0;
        }

        data.Add("X", xData);
        data.Add("Y", yData);

        return data;
    }
    /// <summary>
    /// 2. DAQbuffer数据到二维数组的快速转换
    /// DAQbuffer="";
    /// DoubleArray array;
    ///  Model.posDDAQbufferToArray(DAQbuffer,array);
    ///  retrun flase
    /// </summary>
    /// <param name="Model">模型</param>
    /// <param name="DAQbuffer">buffer</param>
    /// <param name="array">目标数组</param>
    /// <returns></returns>
    public static int posDDAQbufferToArray(this ITemplate Model, String DAQbuffer, DoubleArray array)
    {
        var Buffer = Model.GetVarByName<BufferInputVar>(DAQbuffer);
        Dictionary<string, double[]> keyValuePairs = new();
        int Count = 0;
        //buffer中所有列在array中是否存在，保证buffer中所有值有去处并判断类型
        if (!array.AllkeyContainsKeyAndDouble(Buffer.SignalCodes))
            return -1;
        //拿Buffer值
        foreach (string singnal in Buffer.SignalCodes)
        {
            if (Count == 0)
            {
                Count = Buffer[singnal].Length;
            }
            keyValuePairs.Add(singnal, Buffer[singnal]);
        };
        //循环插值
        for (int i = 0; i < Count; i++)
        {
            Dictionary<string, object> rows = new();
            foreach (var item in keyValuePairs)
            {
                rows.Add(item.Key, item.Value[i]);
            }
            array.AddRowData(rows);
        }
        return 0;
    }
    /// <summary>
    /// 二维数组拷贝所有数据到另外二维数组
    /// ScriptUtils.Array2Copy(Array1,Array2)
    /// </summary>
    /// <param name="Array1">复制数组</param>
    /// <param name="Array2">目标数组</param>
    /// <returns></returns>
    public static void Array2Copy(DoubleArray Array1, DoubleArray Array2)
    {
        Array2 = Array1.DeepCopy();
    }

    /// <summary>
    /// 将二维数组某一个列拷贝到一个double类型的二维浮点类型数组的某个列上（该dlbArray需要提前new足够的行数）
    /// ScriptUtils.Array2TodoubleA
    /// </summary>
    /// <param name="Array1"></param>
    /// <param name="Acol">二维数组的列</param>
    /// <param name="dblArray"></param>
    /// <param name="dblcol">二维浮点数组的列</param>
    /// <returns></returns>
    public static int Array2TodoubleA(DoubleArray Array1, string Acol, DoubleArray dblArray, string dblcol)
    {
      var doubleArray=  Array1.GetColumnObject(Acol);
     return   dblArray.SetCellValueDouble(dblcol, doubleArray.Values);
    }
    /// <summary>
    /// 查询结果变量（尚未完成）
    /// </summary>
    /// <param name="template"></param>
    public static void GetResultVarTableData(this ITemplate template)
    {
         //查询
         var d= template.Db.Connection.Query($"select * FROM Results where sample_instance_code={template.CurrentInst.Code};");
    }
    /// <summary>
    /// 获得试样是否正在执行
    /// Model.GetSampleInstState();
    /// 返回true正在执行返回false未在执行
    /// </summary>
    /// <param name="template"></param>
    /// <returns></returns>
    public static bool GetSampleInstState(this ITemplate template)
    {
        if (template.CurrentInst.State== "RUNNING")
        {
            return true;
        }
        return false;
    }
    /// <summary>
    /// Json(string类型)转换为Dictionary
    /// string str2 = "0 0 {\"CMD\":11,\"ParaType\":1,\"startupstatus\":0}";
    /// Dictionary<string, dynamic> dic = Scripting.ScriptUtils.JsontoDictionary(
    /// str2.Split("|").Last());
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static Dictionary<string, dynamic> JsontoDictionary(string str)
    {
        var ret = JsonDocument.Parse(str).RootElement.JsontoDictionary();
        return ret;
    }
    /// <summary>
    /// Json转换为Dictionary
    /// 使用方法：
    /// JsonElement element;//Json对象
    /// element.JsontoDictionary();
    /// </summary>
    /// <param name="element">Json参数</param>
    /// <returns></returns>
    public static Dictionary<string, dynamic> JsontoDictionary(this JsonElement element)
    {
        Dictionary<string, dynamic> dic = new();
        foreach (var property in element.EnumerateObject())
        {
            JsonValueKind valueKind = property.Value.ValueKind;
            switch (valueKind)
            {
                case JsonValueKind.Number:
                    // 处理数字类型的值
                    double numberValue = property.Value.GetDouble();
                    dic[property.Name] = numberValue;
                    break;
                case JsonValueKind.String:
                    // 处理字符串类型的值
                    string stringValue = property.Value.GetString();
                    dic[property.Name] = stringValue;
                    break;
                case JsonValueKind.Object:
                    // 如果属性值是对象类型，则递归处理
                    if (property.Value.ValueKind == JsonValueKind.Object)
                    {
                        // 递归调用构造函数处理对象类型的属性值
                        var nestedObject = JsontoDictionary(property.Value);
                        // 将递归处理后的结果存储到当前字典中
                        dic[property.Name] = nestedObject;
                    }
                    break;
                case JsonValueKind.Array:
                    // 处理数组类型的值，可以进行遍历处理
                    // 这里可以根据需要进行遍历处理
                    break;
                case JsonValueKind.True:
                case JsonValueKind.False:
                    // 处理布尔类型的值
                    bool boolValue = property.Value.GetBoolean();
                    dic[property.Name] = boolValue;
                    break;
                case JsonValueKind.Null:
                    // 处理空值 根据实际情况进行处理
                    dic[property.Name] = null;
                    break;
            }
        }
        return dic;
    }
    public static Dictionary<string, bool> VrCycles = new();
    // public static double FindUnilateralCapacityCoefficieWithICF(string filePath,double reliability,double confidenceLevel,int degree0fFreedom)
    // {
    //     return SingleSideToleranceCoefficientLookupTableDll.FindUnilateralCapacityCoefficieWithICF(filePath, ICF, Tolerance, MaxCycle);
    // }
    /// <summary>
    /// 虚拟周期数信号变量
    /// </summary>
    /// <param name="Model">模型</param>
    /// <param name="hwDataBlock">block</param>
    /// <param name="VrCycleName">虚拟变量名</param>
    /// <returns></returns>
    public static double[] GetVrCycle(this ITemplate Model,Dictionary<string, double[]> SignalData, string signal_cycle, string OffsetVar = "")
    {

        if (Model.SignalVars.TryGetValue(signal_cycle, out var cycles))
        {
            if (cycles.DeviceId == -1 && cycles.SensorId == -1 && !string.IsNullOrWhiteSpace(cycles.StrScript))
            {
                return new double[0];
            }
        }
        else
        {
            return new double[0];
        }
        int dupflag = 0;
        if (OffsetVar != "")
        {
            if (!Model.iVariable.TryGetValue(OffsetVar, out dupflag))
            {
                Model.iVariable.Add(OffsetVar, -1);
            }
        }
        Func<string, double[]> GetSignalData = (signalName) => SignalData[signalName];
        double Offset = OffsetVar == "" ? 0 : Model.GetVarByName<NumberInputVar>(OffsetVar).Value;
        double[] real_cycle = GetSignalData(signal_cycle);
        double[] virtual_cycle = new double[real_cycle.Length];
        for (int i = 0; i < real_cycle.Length; i++)
        {
            if (dupflag == -1 && real_cycle[i] == 0)
            {
                Model.iVariable[OffsetVar] = 0;
                dupflag = 0;
            }
            if (dupflag == -1)
            {
                virtual_cycle[i] = Offset;
            }
            else
            {
                virtual_cycle[i] = real_cycle[i] + Offset;
            }
        }
        return virtual_cycle;
    }
    
}

