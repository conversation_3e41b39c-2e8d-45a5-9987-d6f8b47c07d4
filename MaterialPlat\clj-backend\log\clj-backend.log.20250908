25-09-08 09:16:31:492 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-08 09:16:31:821 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-08 09:16:32:001 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-08 09:16:32:079 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-08 09:16:32:738 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-08 09:16:32:744 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-08 09:16:55:309 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "【超级管理员】admin: 导入模板或项目"}
25-09-08 09:16:55:317 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\temp_directory_19926e5bef0\instance 成功
25-09-08 09:16:55:319 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\temp_directory_19926e5bef0 成功
25-09-08 09:17:58:365 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_31 应用性能优化配置
25-09-08 09:17:58:366 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_31 项目库连接
25-09-08 09:17:58:608 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_31"}
25-09-08 09:17:58:641 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-08 09:18:03:236 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "d56e72cb-4bd8-41e2-a35f-a1e8267b5deb", :code 0, :msg "模板生成成功"}
25-09-08 09:18:03:239 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-d56e72cb-4bd8-41e2-a35f-a1e8267b5deb 中添加消息 {:ProcessId "d56e72cb-4bd8-41e2-a35f-a1e8267b5deb", :code 0, :msg "模板生成成功"}
25-09-08 09:18:03:241 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "d56e72cb-4bd8-41e2-a35f-a1e8267b5deb", :code 0, :msg "模板生成成功"}
25-09-08 09:18:04:315 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_31", :CurrentInstCode "sample_14785d372", :SelectedInstCodes []} 
 =============================================================

25-09-08 09:18:11:316 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【打开项目默认执行动作】"}
25-09-08 09:18:11:324 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-08 09:18:11:675 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【打开项目默认执行动作】"}
25-09-08 09:18:11:677 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-08 09:18:15:204 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【联机】"}
25-09-08 09:18:15:207 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-08 09:18:25:036 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【启动】"}
25-09-08 09:18:25:038 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-08 09:18:25:133 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【启动】"}
25-09-08 09:18:25:135 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-08 09:18:26:685 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【终止预制裂纹】"}
25-09-08 09:18:26:688 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :State "running"} 
 =============================================================

25-09-08 09:18:26:787 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e", :MsgBody {:Cmd "abort", :InstCode "sample_14785d372", :ActionID "ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-08 09:18:26:788 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "流程图未开始或已结束"}
25-09-08 09:18:26:790 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:39] - 发生系统错误. 
错误码: 1500 
错误描述: 流程图未开始或已结束
25-09-08 09:18:27:826 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【预制裂纹流程图】"}
25-09-08 09:18:27:830 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16", :State "running"} 
 =============================================================

25-09-08 09:18:38:543 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "cbccd622-f63a-47fc-aaa9-543d1983fb5b", :Result true}
25-09-08 09:18:38:544 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-cbccd622-f63a-47fc-aaa9-543d1983fb5b 中添加消息 {:ProcessId "project_31", :ScriptId "cbccd622-f63a-47fc-aaa9-543d1983fb5b", :Result true}
25-09-08 09:18:38:545 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "cbccd622-f63a-47fc-aaa9-543d1983fb5b", :Result true}
25-09-08 09:18:38:772 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "eb93dac5-c647-4ffc-8d42-002a3f6edcde", :Result true}
25-09-08 09:18:38:775 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-eb93dac5-c647-4ffc-8d42-002a3f6edcde 中添加消息 {:ProcessId "project_31", :ScriptId "eb93dac5-c647-4ffc-8d42-002a3f6edcde", :Result true}
25-09-08 09:18:38:777 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "eb93dac5-c647-4ffc-8d42-002a3f6edcde", :Result true}
25-09-08 09:18:38:778 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-08 09:18:38:787 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【关闭项目时默认执行动作】"}
25-09-08 09:18:38:792 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-08 09:18:39:127 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 31, :user_id 1}
25-09-08 09:18:39:140 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【关闭项目时默认执行动作】"}
25-09-08 09:18:39:142 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-08 09:18:39:231 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【终止预制裂纹】"}
25-09-08 09:18:39:236 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :State "finished"} 
 =============================================================

25-09-08 09:18:39:253 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-08 09:18:39:362 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-08 09:18:39:364 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-08 09:18:39:367 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "保存了项目"}
25-09-08 09:18:39:437 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757294214896_project_31_copy.db 成功
25-09-08 09:18:39:464 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-08 09:18:42:554 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "3fe5fe23-5dbf-460c-8c2b-a0b30a1e4a33", :Result true}
25-09-08 09:18:42:555 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-3fe5fe23-5dbf-460c-8c2b-a0b30a1e4a33 中添加消息 {:ProcessId "project_31", :ScriptId "3fe5fe23-5dbf-460c-8c2b-a0b30a1e4a33", :Result true}
25-09-08 09:18:42:556 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "3fe5fe23-5dbf-460c-8c2b-a0b30a1e4a33", :Result true}
25-09-08 09:18:42:882 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "3fc53a36-eaac-41b5-8e30-6e9a0ae300c7", :Result true}
25-09-08 09:18:42:883 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-3fc53a36-eaac-41b5-8e30-6e9a0ae300c7 中添加消息 {:ProcessId "project_31", :ScriptId "3fc53a36-eaac-41b5-8e30-6e9a0ae300c7", :Result true}
25-09-08 09:18:42:884 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "3fc53a36-eaac-41b5-8e30-6e9a0ae300c7", :Result true}
25-09-08 09:18:43:820 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "154c7153-587e-430e-a3da-c4c16cd690d2", :Result true}
25-09-08 09:18:43:821 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-154c7153-587e-430e-a3da-c4c16cd690d2 中添加消息 {:ProcessId "project_31", :ScriptId "154c7153-587e-430e-a3da-c4c16cd690d2", :Result true}
25-09-08 09:18:43:822 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "154c7153-587e-430e-a3da-c4c16cd690d2", :Result true}
25-09-08 09:20:59:991 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【终止预制裂纹】"}
25-09-08 09:20:59:993 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :State "running"} 
 =============================================================

25-09-08 09:21:00:273 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e", :MsgBody {:Cmd "abort", :InstCode "sample_14785d372", :ActionID "ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-08 09:21:00:280 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【预制裂纹流程图】"}
25-09-08 09:21:00:282 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16", :State "finished"} 
 =============================================================

25-09-08 09:21:01:587 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-08 09:21:01:593 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【关闭项目时默认执行动作】"}
25-09-08 09:21:01:595 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-08 09:21:01:705 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 31, :user_id 1}
25-09-08 09:21:01:715 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【关闭项目时默认执行动作】"}
25-09-08 09:21:01:717 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-08 09:21:01:814 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-08 09:21:01:849 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【终止预制裂纹】"}
25-09-08 09:21:01:852 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :State "finished"} 
 =============================================================

25-09-08 09:21:01:924 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-08 09:21:01:925 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-08 09:21:01:927 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "保存了项目"}
25-09-08 09:21:01:973 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757294214896_project_31_copy.db 成功
25-09-08 09:21:01:999 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-08 09:21:16:439 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【裂纹检查新版流程图】"}
25-09-08 09:21:16:442 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-08 09:21:16:575 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "7643a6e3-e740-45a2-82f8-54034246a62a", :Result true}
25-09-08 09:21:16:576 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-7643a6e3-e740-45a2-82f8-54034246a62a 中添加消息 {:ProcessId "project_31", :ScriptId "7643a6e3-e740-45a2-82f8-54034246a62a", :Result true}
25-09-08 09:21:16:577 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "7643a6e3-e740-45a2-82f8-54034246a62a", :Result true}
25-09-08 09:21:16:871 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "96fb8016-b5b4-4410-b74f-baf9a5a11956", :Result true}
25-09-08 09:21:16:872 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-96fb8016-b5b4-4410-b74f-baf9a5a11956 中添加消息 {:ProcessId "project_31", :ScriptId "96fb8016-b5b4-4410-b74f-baf9a5a11956", :Result true}
25-09-08 09:21:16:873 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "96fb8016-b5b4-4410-b74f-baf9a5a11956", :Result true}
25-09-08 09:21:36:875 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【终止裂纹长度检查】"}
25-09-08 09:21:36:877 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-08 09:21:36:934 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_14785d372", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-08 09:21:36:939 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【裂纹检查新版流程图】"}
25-09-08 09:21:36:940 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-08 09:21:37:012 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-08 09:21:37:015 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【横梁停止】"}
25-09-08 09:21:37:019 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-08 09:21:37:130 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "159dd041-7265-4fe7-bdc1-c6741565cbe4", :Result true}
25-09-08 09:21:37:130 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-159dd041-7265-4fe7-bdc1-c6741565cbe4 中添加消息 {:ProcessId "project_31", :ScriptId "159dd041-7265-4fe7-bdc1-c6741565cbe4", :Result true}
25-09-08 09:21:37:132 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "159dd041-7265-4fe7-bdc1-c6741565cbe4", :Result true}
25-09-08 09:21:37:439 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【横梁停止】"}
25-09-08 09:21:37:441 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-08 09:21:37:622 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【终止裂纹长度检查】"}
25-09-08 09:21:37:624 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-08 09:21:40:001 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【裂纹检查新版流程图】"}
25-09-08 09:21:40:003 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-08 09:21:40:098 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "1eb85427-1904-42e9-9100-afd78db695be", :Result true}
25-09-08 09:21:40:099 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-1eb85427-1904-42e9-9100-afd78db695be 中添加消息 {:ProcessId "project_31", :ScriptId "1eb85427-1904-42e9-9100-afd78db695be", :Result true}
25-09-08 09:21:40:100 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "1eb85427-1904-42e9-9100-afd78db695be", :Result true}
25-09-08 09:21:40:427 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "3e349169-bfe6-4515-8672-5289b8b24383", :Result true}
25-09-08 09:21:40:427 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-3e349169-bfe6-4515-8672-5289b8b24383 中添加消息 {:ProcessId "project_31", :ScriptId "3e349169-bfe6-4515-8672-5289b8b24383", :Result true}
25-09-08 09:21:40:428 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "3e349169-bfe6-4515-8672-5289b8b24383", :Result true}
25-09-08 09:22:07:764 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【裂纹检查新版流程图】"}
25-09-08 09:22:07:765 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-08 09:22:46:004 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【裂纹检查新版流程图】"}
25-09-08 09:22:46:006 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-08 09:22:46:090 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "477d6f81-4dd4-476e-98b0-39c5aec2957f", :Result true}
25-09-08 09:22:46:091 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-477d6f81-4dd4-476e-98b0-39c5aec2957f 中添加消息 {:ProcessId "project_31", :ScriptId "477d6f81-4dd4-476e-98b0-39c5aec2957f", :Result true}
25-09-08 09:22:46:092 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "477d6f81-4dd4-476e-98b0-39c5aec2957f", :Result true}
25-09-08 09:22:46:410 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "d6e57472-8b4b-4d39-9390-ec12749fcfc1", :Result false}
25-09-08 09:22:46:410 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-d6e57472-8b4b-4d39-9390-ec12749fcfc1 中添加消息 {:ProcessId "project_31", :ScriptId "d6e57472-8b4b-4d39-9390-ec12749fcfc1", :Result false}
25-09-08 09:22:46:411 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "d6e57472-8b4b-4d39-9390-ec12749fcfc1", :Result false}
25-09-08 09:23:21:756 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【裂纹检查新版流程图】"}
25-09-08 09:23:21:757 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-08 09:25:41:065 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【预制裂纹模拟曲线】"}
25-09-08 09:25:41:068 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f", :State "running"} 
 =============================================================

25-09-08 09:25:41:115 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_31", :ProcessID "project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f", :SubTaskID "onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "952ef7e6-11e2-4006-a1dd-ab5ac9fecd98"}}
25-09-08 09:25:41:119 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【试样校验】"}
25-09-08 09:25:41:120 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98", :State "running"} 
 =============================================================

25-09-08 09:25:41:266 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "b3eda46d-b234-40ec-8f63-2b802028cb5e", :Result false}
25-09-08 09:25:41:267 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b3eda46d-b234-40ec-8f63-2b802028cb5e 中添加消息 {:ProcessId "project_31", :ScriptId "b3eda46d-b234-40ec-8f63-2b802028cb5e", :Result false}
25-09-08 09:25:41:268 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "b3eda46d-b234-40ec-8f63-2b802028cb5e", :Result false}
25-09-08 09:25:41:340 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "7824c123-c762-4906-bc00-48074fc30043", :Result true}
25-09-08 09:25:41:340 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-7824c123-c762-4906-bc00-48074fc30043 中添加消息 {:ProcessId "project_31", :ScriptId "7824c123-c762-4906-bc00-48074fc30043", :Result true}
25-09-08 09:25:41:341 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "7824c123-c762-4906-bc00-48074fc30043", :Result true}
25-09-08 09:25:41:356 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "8ac2932d-9e77-4691-b404-74ce9536e579", :Result true}
25-09-08 09:25:41:356 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-8ac2932d-9e77-4691-b404-74ce9536e579 中添加消息 {:ProcessId "project_31", :ScriptId "8ac2932d-9e77-4691-b404-74ce9536e579", :Result true}
25-09-08 09:25:41:358 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "8ac2932d-9e77-4691-b404-74ce9536e579", :Result true}
25-09-08 09:25:41:364 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【试样校验】"}
25-09-08 09:25:41:365 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98", :State "finished"} 
 =============================================================

25-09-08 09:25:41:444 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "c43e4ebd-fa0b-44f8-bb64-7fbe3b37559a", :Result true}
25-09-08 09:25:41:444 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-c43e4ebd-fa0b-44f8-bb64-7fbe3b37559a 中添加消息 {:ProcessId "project_31", :ScriptId "c43e4ebd-fa0b-44f8-bb64-7fbe3b37559a", :Result true}
25-09-08 09:25:41:445 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "c43e4ebd-fa0b-44f8-bb64-7fbe3b37559a", :Result true}
25-09-08 09:25:41:597 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【预制裂纹模拟曲线】"}
25-09-08 09:25:41:600 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f", :State "finished"} 
 =============================================================

25-09-08 09:25:43:668 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【预制裂纹流程图】"}
25-09-08 09:25:43:670 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16", :State "running"} 
 =============================================================

25-09-08 09:25:43:793 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "aaa33d98-2c6c-4d52-a9ee-23699ae330cd", :Result true}
25-09-08 09:25:43:794 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-aaa33d98-2c6c-4d52-a9ee-23699ae330cd 中添加消息 {:ProcessId "project_31", :ScriptId "aaa33d98-2c6c-4d52-a9ee-23699ae330cd", :Result true}
25-09-08 09:25:43:794 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "aaa33d98-2c6c-4d52-a9ee-23699ae330cd", :Result true}
25-09-08 09:25:45:135 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "e32d58fe-037e-47eb-941b-52bd53bfca1f", :Result true}
25-09-08 09:25:45:135 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-e32d58fe-037e-47eb-941b-52bd53bfca1f 中添加消息 {:ProcessId "project_31", :ScriptId "e32d58fe-037e-47eb-941b-52bd53bfca1f", :Result true}
25-09-08 09:25:45:137 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "e32d58fe-037e-47eb-941b-52bd53bfca1f", :Result true}
25-09-08 09:25:45:188 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "f18de07b-14e7-41f6-8caf-37219d8aa6aa", :Result true}
25-09-08 09:25:45:189 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-f18de07b-14e7-41f6-8caf-37219d8aa6aa 中添加消息 {:ProcessId "project_31", :ScriptId "f18de07b-14e7-41f6-8caf-37219d8aa6aa", :Result true}
25-09-08 09:25:45:190 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "f18de07b-14e7-41f6-8caf-37219d8aa6aa", :Result true}
25-09-08 09:25:45:933 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_31", :ScriptId "10cb7dcd-3fe4-42c1-a5ab-ad615ef222df", :Result true}
25-09-08 09:25:45:934 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-10cb7dcd-3fe4-42c1-a5ab-ad615ef222df 中添加消息 {:ProcessId "project_31", :ScriptId "10cb7dcd-3fe4-42c1-a5ab-ad615ef222df", :Result true}
25-09-08 09:25:45:935 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_31", :ScriptId "10cb7dcd-3fe4-42c1-a5ab-ad615ef222df", :Result true}
25-09-08 10:20:40:430 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【关闭项目时默认执行动作】"}
25-09-08 10:20:40:432 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-08 10:20:40:499 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【关闭项目重置参数】"}
25-09-08 10:20:40:501 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-08 10:20:40:547 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-08 10:20:40:548 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_31】项目"}
25-09-08 10:20:40:553 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【打开项目默认执行动作】"}
25-09-08 10:20:40:557 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【终止裂纹长度检查】"}
25-09-08 10:20:40:563 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【启动】"}
25-09-08 10:20:40:573 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【横梁停止】"}
25-09-08 10:20:40:578 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【关闭项目时默认执行动作】"}
25-09-08 10:20:40:579 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-08 10:20:40:667 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【试样校验】"}
25-09-08 10:20:40:670 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【联机】"}
25-09-08 10:20:40:671 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-08 10:20:40:739 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【预制裂纹模拟曲线】"}
25-09-08 10:20:40:746 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【裂纹检查新版流程图】"}
25-09-08 10:20:40:751 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【关闭项目重置参数】"}
25-09-08 10:20:40:752 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-08 10:20:40:839 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【预制裂纹流程图】"}
25-09-08 10:20:40:842 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16", :State "finished"} 
 =============================================================

25-09-08 10:20:40:903 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【终止预制裂纹】"}
25-09-08 10:20:41:420 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 31, :user_id 1}
25-09-08 10:20:41:536 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-08 10:20:41:646 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-08 10:20:41:648 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-08 10:20:41:650 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "保存了项目"}
25-09-08 10:20:41:701 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757294214896_project_31_copy.db 成功
25-09-08 10:20:41:731 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-08 10:20:42:670 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757294214896_project_31.db 成功
25-09-08 10:20:42:723 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_31 应用性能优化配置
25-09-08 10:20:42:723 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_31 项目库连接
25-09-08 10:25:42:474 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-08 10:25:42:520 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-08 10:25:42:569 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-08 10:25:42:604 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-08 10:25:50:243 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_31"}
25-09-08 10:25:50:271 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-08 10:25:51:450 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "2a723568-2f8f-4dfe-95a8-09f0b4d1a22f", :code 0, :msg "模板生成成功"}
25-09-08 10:25:51:451 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-2a723568-2f8f-4dfe-95a8-09f0b4d1a22f 中添加消息 {:ProcessId "2a723568-2f8f-4dfe-95a8-09f0b4d1a22f", :code 0, :msg "模板生成成功"}
25-09-08 10:25:51:452 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "2a723568-2f8f-4dfe-95a8-09f0b4d1a22f", :code 0, :msg "模板生成成功"}
25-09-08 10:25:52:650 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【打开项目默认执行动作】"}
25-09-08 10:25:52:652 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-08 10:25:54:483 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【打开项目默认执行动作】"}
25-09-08 10:25:54:484 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-08 10:27:06:071 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:643] - Project event listener stopping.
25-09-08 10:27:06:075 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-08 10:27:09:665 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:668 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:670 DESKTOP-3BSREDP WARN [clj-backend.db.connections:88] - [连接监控] 创建新的项目库连接 - 连接ID: project_31 项目目录: D:/WorkProject/ZJ/MaterialPlat/clj-backend/db
25-09-08 10:27:09:671 DESKTOP-3BSREDP WARN [clj-backend.db.connections:23] - [连接监控] 开始创建数据库连接 - 连接ID: project_31 数据库路径: D:/WorkProject/ZJ/MaterialPlat/clj-backend/db/1757294214896_project_31.db
25-09-08 10:27:09:673 DESKTOP-3BSREDP WARN [clj-backend.db.connections:26] - [连接监控] 数据库连接创建成功 - 连接ID: project_31 耗时: 1 ms
25-09-08 10:27:09:674 DESKTOP-3BSREDP WARN [clj-backend.db.connections:29] - [连接监控] 开始应用性能优化配置 - 连接ID: project_31
25-09-08 10:27:09:678 DESKTOP-3BSREDP WARN [clj-backend.db.connections:44] - [连接监控] 性能优化配置应用成功 - 连接ID: project_31
25-09-08 10:27:09:680 DESKTOP-3BSREDP WARN [clj-backend.db.connections:48] - [连接监控] 连接已加入连接池 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:681 DESKTOP-3BSREDP WARN [clj-backend.db.connections:98] - [连接监控] 项目库连接创建完成 - 连接ID: project_31
25-09-08 10:27:09:683 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 15 ms 当前连接池大小: 1
25-09-08 10:27:09:688 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:688 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:689 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:690 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:09:714 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:715 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:717 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:717 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:09:784 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:786 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:786 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:787 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 10:27:09:814 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:816 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:817 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:818 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:09:842 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:843 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:845 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:845 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:09:869 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:871 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:872 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:873 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:09:932 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:09:934 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:09:935 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:09:941 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 7 ms 当前连接池大小: 1
25-09-08 10:27:11:430 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:11:431 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:11:432 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:11:432 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 10:27:11:539 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:11:540 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:11:541 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:11:541 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 10:27:11:672 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:11:673 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:11:674 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:11:674 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 10:27:11:694 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:11:695 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:11:696 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:11:697 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:11:725 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:11:726 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:11:727 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:11:728 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:11:747 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:11:748 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:11:749 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:11:750 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:11:776 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:11:777 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:11:778 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:11:779 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 10:27:12:149 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 10:27:12:150 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 10:27:12:150 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 10:27:12:151 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 11:05:23:301 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 11:05:23:303 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 11:05:23:304 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 11:05:23:304 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 11:05:23:312 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【关闭项目时默认执行动作】"}
25-09-08 11:05:23:317 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-08 11:05:23:391 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【关闭项目重置参数】"}
25-09-08 11:05:23:392 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-08 11:05:23:424 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-08 11:05:23:426 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_31】项目"}
25-09-08 18:06:58:355 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-08 18:06:58:716 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-08 18:06:58:904 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-08 18:06:58:995 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-08 18:06:59:354 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: nil
25-09-08 18:06:59:357 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: nil 耗时: 0 ms 当前连接池大小: 0
25-09-08 18:06:59:388 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: nil
25-09-08 18:06:59:390 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: nil 耗时: 0 ms 当前连接池大小: 0
25-09-08 18:06:59:633 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-08 18:06:59:639 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-08 18:07:01:800 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_31"}
25-09-08 18:07:01:802 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:01:803 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:01:805 DESKTOP-3BSREDP WARN [clj-backend.db.connections:88] - [连接监控] 创建新的项目库连接 - 连接ID: project_31 项目目录: D:/WorkProject/ZJ/MaterialPlat/clj-backend/db
25-09-08 18:07:01:807 DESKTOP-3BSREDP WARN [clj-backend.db.connections:23] - [连接监控] 开始创建数据库连接 - 连接ID: project_31 数据库路径: D:/WorkProject/ZJ/MaterialPlat/clj-backend/db/1757294214896_project_31.db
25-09-08 18:07:01:809 DESKTOP-3BSREDP WARN [clj-backend.db.connections:26] - [连接监控] 数据库连接创建成功 - 连接ID: project_31 耗时: 2 ms
25-09-08 18:07:01:810 DESKTOP-3BSREDP WARN [clj-backend.db.connections:29] - [连接监控] 开始应用性能优化配置 - 连接ID: project_31
25-09-08 18:07:01:813 DESKTOP-3BSREDP WARN [clj-backend.db.connections:44] - [连接监控] 性能优化配置应用成功 - 连接ID: project_31
25-09-08 18:07:01:814 DESKTOP-3BSREDP WARN [clj-backend.db.connections:48] - [连接监控] 连接已加入连接池 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:01:815 DESKTOP-3BSREDP WARN [clj-backend.db.connections:98] - [连接监控] 项目库连接创建完成 - 连接ID: project_31
25-09-08 18:07:01:816 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 13 ms 当前连接池大小: 1
25-09-08 18:07:01:866 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-08 18:07:08:366 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "81d83f07-7dcd-45d9-b271-640c795af782", :code 0, :msg "模板生成成功"}
25-09-08 18:07:08:368 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-81d83f07-7dcd-45d9-b271-640c795af782 中添加消息 {:ProcessId "81d83f07-7dcd-45d9-b271-640c795af782", :code 0, :msg "模板生成成功"}
25-09-08 18:07:08:371 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "81d83f07-7dcd-45d9-b271-640c795af782", :code 0, :msg "模板生成成功"}
25-09-08 18:07:08:425 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:426 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:427 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:428 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:536 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:543 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:543 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:546 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:08:570 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:572 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:574 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:575 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:08:599 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:601 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:601 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:603 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:626 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:628 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:630 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:631 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:08:655 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:657 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:658 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:660 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:08:684 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:686 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:687 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:688 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:711 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:712 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:714 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:715 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:08:738 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:740 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:741 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:742 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:765 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:767 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:768 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:769 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:793 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:794 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:795 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:797 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:08:822 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:823 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:824 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:826 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:08:845 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:846 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:847 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:848 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:898 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:901 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:902 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:903 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:925 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:927 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:928 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:929 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:08:938 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:939 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:939 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:940 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:07:08:971 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:08:973 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:08:975 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:08:975 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:09:165 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:167 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:169 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:170 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:09:239 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:241 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:243 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:244 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:09:267 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:269 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:272 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:274 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 5 ms 当前连接池大小: 1
25-09-08 18:07:09:299 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:300 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:302 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:304 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 4 ms 当前连接池大小: 1
25-09-08 18:07:09:331 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:333 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:335 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:336 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:09:364 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:368 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:371 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:372 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 4 ms 当前连接池大小: 1
25-09-08 18:07:09:421 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:423 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:425 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:427 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 4 ms 当前连接池大小: 1
25-09-08 18:07:09:452 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:454 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:455 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:456 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:09:481 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:482 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:484 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:485 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:09:496 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:497 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:499 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:500 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:09:532 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:534 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:536 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:537 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:09:558 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:560 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:561 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:562 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:09:636 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:637 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:638 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:640 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:09:658 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_31", :CurrentInstCode "sample_14785d372", :SelectedInstCodes []} 
 =============================================================

25-09-08 18:07:09:695 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:696 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:697 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:698 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:09:764 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:766 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:766 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:767 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:07:09:823 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:824 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:825 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:826 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:09:850 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:851 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:854 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:855 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 4 ms 当前连接池大小: 1
25-09-08 18:07:09:879 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:882 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:882 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:884 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:09:910 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:911 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:912 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:913 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:07:09:938 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:09:939 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:09:939 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:09:940 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:07:10:148 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:10:151 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:10:153 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:10:154 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:10:400 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:10:404 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:10:405 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:10:405 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:07:23:292 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:23:293 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:23:293 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:23:294 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:07:23:681 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:07:23:682 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:07:23:684 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:07:23:685 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:07:23:696 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "开始流程【打开项目默认执行动作】"}
25-09-08 18:07:23:709 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-08 18:07:25:331 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 31, :project_name "K1C试验项目0907", :content "结束流程【打开项目默认执行动作】"}
25-09-08 18:07:25:334 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_31", :ProcessId "project_31-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-08 18:10:26:257 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:10:26:260 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:10:26:262 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:10:26:263 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 3 ms 当前连接池大小: 1
25-09-08 18:10:26:376 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:10:26:377 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:10:26:377 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:10:26:378 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:10:37:986 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:10:37:987 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:10:37:988 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:10:37:989 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:11:05:634 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:11:05:636 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:11:05:636 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:11:05:637 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:11:16:174 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:11:16:175 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:11:16:176 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:11:16:176 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:16:00:777 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:16:00:780 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:16:00:781 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:16:00:782 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 18:16:00:872 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:16:00:873 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:16:00:874 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:16:00:874 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:16:11:145 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:16:11:147 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:16:11:148 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:16:11:148 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 18:16:16:172 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 18:16:16:173 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 18:16:16:174 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 18:16:16:174 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 19:23:00:056 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 19:23:00:059 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 19:23:00:060 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 19:23:00:061 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 2 ms 当前连接池大小: 1
25-09-08 19:23:00:232 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 19:23:00:233 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 19:23:00:233 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 19:23:00:234 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
25-09-08 19:23:03:186 DESKTOP-3BSREDP WARN [clj-backend.db.connections:107] - [连接监控] 请求获取数据库连接 - 模板ID: nil 项目ID: 31
25-09-08 19:23:03:187 DESKTOP-3BSREDP WARN [clj-backend.db.connections:75] - [连接监控] 请求获取项目库连接 - 项目ID: 31
25-09-08 19:23:03:188 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - [连接监控] 复用现有项目库连接 - 连接ID: project_31 当前连接池大小: 1
25-09-08 19:23:03:188 DESKTOP-3BSREDP WARN [clj-backend.db.connections:119] - [连接监控] 数据库连接获取完成 - 模板ID: nil 项目ID: 31 耗时: 1 ms 当前连接池大小: 1
