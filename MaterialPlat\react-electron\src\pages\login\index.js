import React, { useEffect, useState, useRef } from 'react'
import { message, Checkbox } from 'antd'
import { useTranslation } from 'react-i18next'
import moment from 'moment'

import useLogin from '@/hooks/useLogin'
import { newSee, newNoSee } from '@/static/img/index'
import useUnit from '@/hooks/useUnit'
import useSubTask from '@/hooks/useSubTask'
import useStation from '@/hooks/useStation'
import { PreLoad } from '@/routers/style'
import useWidget from '@/hooks/useWidget'
import useModuleDataSource from '@/hooks/useModuleDataSource'
import useAudio from '@/hooks/useAudio'
import useProjectList from '@/hooks/systemInfo/useProjectList'
import { useSelector, useDispatch } from 'react-redux'
import RCLoading from '@/components/loading'
import useHardware from '@/hooks/useHardware'
import useSystem, { APP_WORK_MODE } from '@/hooks/useSystem'
import { DIALOG_SPOT_CHECK_REMIND } from '@/redux/constants/dialog'
import { getEndTime, getNeedInspectCount } from '@/utils/services'
import { GLOBAL_USER_END_TIME, GLOBAL_IS_SERVER_SUCCESS, UPDATE_LOGIN_STATUS } from '@/redux/constants/global'
import VerifyServer from '@/components/verifyServer/index'
import useDialog from '@/hooks/useDialog'
import useStandby from '@/hooks/useStandby'
import useGlobalMonitoring from '@/hooks/useGlobalMonitoring'
import { addGlobalLoading, removeGlobalLoading } from '@/redux/action/globalLoading'
import useElectron from '@/hooks/useElectron'

import SettingModal from './SettingModal'
import { LoginContainer } from './style'
import { INIT_VALUES } from './constants'

const Login = () => {
    const dispatch = useDispatch()
    const { initModuleData } = useModuleDataSource()
    const { doLogin } = useLogin()
    const { initSystemConfig } = useSystem()
    const { initUnitsData } = useUnit()
    const { initHardwareList } = useHardware()
    const { initAudioData } = useAudio()
    const { initStationInfo } = useStation()
    const { openDialog } = useDialog()
    const { initStandByConfig } = useStandby()
    const { initGlobalProjectID } = useGlobalMonitoring()
    const { initProjectList } = useProjectList()

    const { initPair, initPublisher, initUiPair } = useSubTask()
    const loading = useSelector(state => state.global.loading)
    const loadingName = useSelector(state => state.global.loadingName)
    const isServerSuccess = useSelector(state => state.global.isServerSuccess)
    const { initWidget } = useWidget()

    const [messageApi, contextHolder] = message.useMessage()
    const [param, setParam] = useState({ ...INIT_VALUES })
    const [see, setSee] = useState(false)
    const { t } = useTranslation()
    const [open, setOpen] = useState(false)

    const {
        updateTitle
    } = useElectron()

    useEffect(() => {
        updateTitle()
        setParam({ ...INIT_VALUES, softwareConfig: false })
    }, [])

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            handleSubmit()
        }
    }

    const handleOnChange = e => {
        setParam({
            ...param,
            [e.target.name]: e.target.value
        })
    }

    const getNeedInspectCounts = async (mode) => {
        if (mode === APP_WORK_MODE['硬件配置模式']) {
            return
        }
        const res = await getNeedInspectCount()
        if (res) {
            if (res.need_today_inspect_count === 0 && res.need_recent_inspect_count === 0) {
                return
            }
            openDialog({ type: DIALOG_SPOT_CHECK_REMIND })
        }
    }

    const handleSubmit = async () => {
        try {
            if (!param || !param?.account || !param?.password) {
                messageApi.open({
                    type: 'error',
                    content: t(`未填写${param?.account ? '' : '账号'}${param?.password ? '' : '密码'}`)
                })
                return
            }
            if (param?.softwareConfig) {
                setOpen(true)
            } else {
                loginHandle()
            }
        } catch (error) {
            console.error(error)
        }
    }

    const loginHandle = async (mode) => {
        // eslint-disable-next-line no-self-compare
        const loadingId = dispatch(addGlobalLoading('登录中'))
        let res = null
        try {
            res = await doLogin(param, mode)
            if (res === '登陆成功') {
                window?.logger?.info('欢迎, 登录')
                dispatch({ type: GLOBAL_USER_END_TIME, param: moment(await getEndTime()).format('YYYY-MM-DD HH:mm:ss') })
                initPair()
                initUiPair()
                await Promise.all([
                    initSystemConfig(true),
                    getNeedInspectCounts(mode),
                    initUnitsData(),
                    initPublisher(),
                    initWidget(),
                    initModuleData(),
                    initAudioData(),
                    initHardwareList(),
                    initStationInfo(),
                    initStandByConfig(),
                    initGlobalProjectID(),
                    initProjectList()
                ])
            } else if (res?.includes('加密狗错误')) {
                messageApi.open({
                    type: 'error',
                    content: t(res.split('-')[1])
                })
            } else {
                messageApi.open({
                    type: 'error',
                    content: t('账号或密码错误')
                })
            }
            dispatch(removeGlobalLoading(loadingId))
        } catch (e) {
            dispatch(removeGlobalLoading(loadingId))
        }
    }

    const showPassWord = () => {
        setSee(!see)
    }

    const handleSuccess = () => {
        dispatch({ type: GLOBAL_IS_SERVER_SUCCESS, param: true })
    }

    const onOk = (mode) => {
        loginHandle(mode)
        onCancel()
    }
    const onCancel = () => {
        setOpen(false)
    }
    return (
        <>
            <PreLoad />
            {!isServerSuccess
                && <VerifyServer onSuccess={handleSuccess} />}
            <LoginContainer>
                {contextHolder}
                {loading && <RCLoading text={loadingName} />}
                <div className="container">
                    <div className="left-background" />
                    <div className="right-login">
                        <div className="login-container">
                            <div className="app_logo" />
                            <div className="login_title">{t('登录')}</div>
                            <div className="login_tip">{t('欢迎登录中机试验系统')}</div>
                            <div className="login_input_container">
                                <input
                                    id="username"
                                    onChange={handleOnChange}
                                    value={param?.account || ''}
                                    name="account"
                                    className="login_input"
                                    placeholder={t('请输入账号')}
                                />
                            </div>
                            <div className="login_input_container">
                                <input
                                    id="password"
                                    onChange={handleOnChange}
                                    onKeyDown={handleKeyDown}
                                    value={param?.password || ''}
                                    type={see ? 'text' : 'password'}
                                    name="password"
                                    className="login_input"
                                    placeholder={t('请输入密码')}
                                />
                                <img
                                    onClick={showPassWord}
                                    className="login_input_icon"
                                    src={see ? newSee : newNoSee}
                                    alt="see/nosee"
                                />
                            </div>
                            <div className="login_input_container">
                                <Checkbox
                                    onChange={(e) => {
                                        setParam({
                                            ...param,
                                            softwareConfig: e.target.checked
                                        })
                                    }}
                                    checked={param?.softwareConfig || false}
                                >
                                    {t('是否进行软件配置')}
                                </Checkbox>
                            </div>
                            <div className="login_btn_container">
                                <button className="login_btn" id="login-btn" onClick={handleSubmit}>
                                    <span>
                                        {t('登录')}
                                    </span>
                                </button>
                            </div>
                            <div className="helper">
                                {`${t('如有疑问，请联系管理员')}`}
                            </div>
                        </div>
                    </div>
                </div>
            </LoginContainer>

            {
                open
                    ? <SettingModal open={open} onOk={onOk} onCancel={onCancel} /> : null
            }
        </>

    )
}

export default Login
