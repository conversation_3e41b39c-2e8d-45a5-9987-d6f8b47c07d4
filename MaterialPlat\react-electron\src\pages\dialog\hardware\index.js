/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-plusplus */
/* eslint-disable no-lonely-if */
/* eslint-disable no-param-reassign */
/* eslint-disable no-shadow */
/* eslint-disable indent */
import React, {
    useState,
    useRef,
    useMemo,
    useEffect, useCallback
} from 'react'
import { useSelector } from 'react-redux'
import cloneDeep from 'lodash/cloneDeep'
import debounce from 'lodash/debounce'

import { addHardware, getHardware, hardwareRefresh } from '@/utils/services'
import useThrottle from '@/hooks/useThrottle'
import { Space, message } from 'antd'
import VButton from '@/components/vButton/index'
import VModal from '@/components/vModal/index'
import VPage from '@/components/vPage/index'
import { useTranslation } from 'react-i18next'
import { getUserInfo } from '@/utils/auth'
import useHardware from '@/hooks/useHardware'
import { getLabel } from '@/utils/utils'
import { ModalContext } from './components/comStyle'
import TreeCom from './components/TreeCom'
import Logic from './components/Logic'
import HardFormCom from './components/HardFormCom'
import ServoCom from './components/ServoCom' // 伺服控制编辑相关
import HandCom from './components/HandCom'
import InCom from './components/InCom'
import OutCom from './components/OutCom'
import DaCom from './components/DaCom'
import AdCom from './components/AdCom'
import HwDevice from './components/HwDevice'
import RightMenu from './components/RightMenu'
import {
    treeData,
    getNodeById,
    ParentTypeList,
    AxisTypes,
    editNodeById,
    ParentTypes,
    MENU_TYPES,
    HARDWARE_EDIT_TYPES,
    treeFindPath,
    HARWARE_CONST_TYPES,
    HARWARE_AXIS_DATA5,
    HARWARE_EXIT_DATA5,
    HARWARE_HW_DATA5,
    CHILDREN_TYPES,
    HardwareType,
    addIndex
} from './components/contants'

const HardwareModal = ({
    open, setOpen
}) => {
    const { t } = useTranslation()
    const [nowTree, setNowTree] = useState('') // 当前正在编辑的硬件
    const [showLogic, setShowLogic] = useState(false)
    const [selectRow, setSelectRow] = useState() // 此处只记录通道id
    const [hardwareList, setHardwareList] = useState([]) // 硬件列表
    const [menuType, setMenuType] = useState(MENU_TYPES.CONTEXT) // 选择的是编辑tree还是children
    const [treeShowList, setTreeShowList] = useState([]) // 当前tree列表
    const [nowChildrenKey, setNowChildrenKey] = useState() // 选择的children的key
    const nodeComType = useRef()
    const [editType, setEditType] = useState() // 控制右侧编辑展示组件
    const [nowEditData, setNowEditData] = useState({}) // 当前正在编辑的内容
    const [menu, setMenu] = useState({
        add: true,
        remove: true,
        copy: true,
        affix: true
    }) // 菜单权限
    const [logicData, setLogicData] = useState([]) // 全部的tree的合集
    const [modifiedHardwares, setModifiedHardwares] = useState([]) // 编辑及新建的硬件合集
    const [delHardwares, setDelHardwares] = useState([]) // 编辑过程中删除的值
    const [editFlag, setEditFlag] = useState(false) // 是否允许回传
    const rightClickRef = useRef()
    const addChanleDetail = useRef({}) // 暂存即将进行编辑的内容
    const [hardwareDetail, setHardwareDetail] = useState([])
    const [currentData, setCurrentData] = useState({})
    const { initHardwareList } = useHardware()
    const hardwareData = useSelector(state => state.global.hardwareList)
    const copyDataRef = useRef()
    const isAffixRef = useRef(false)

    const nowChildren = useMemo(() => { // 处理当前选择的非children
        const nowList = cloneDeep(hardwareList)
        const newNewList = addIndex(nowList)
        if (!hardwareList?.length || (!nowTree && nowTree !== 0) || !nowChildrenKey) {
            return {}
        }
        const res = getNodeById(newNewList[nowTree].children, nowChildrenKey)
        return res
    }, [hardwareList, nowTree, nowChildrenKey])

    const chanleNode = useMemo(() => { // 处理当前选择的通道类children
        const nowList = cloneDeep(hardwareList)
        const newNewList = addIndex(nowList)
        if (!hardwareList?.length || (!nowTree && nowTree !== 0) || !selectRow) {
            return {}
        }
        const res = getNodeById(newNewList[nowTree].children, selectRow)
        return res
    }, [hardwareList, nowTree, selectRow])

    const adParentType = useMemo(() => {
        if (chanleNode?.parentAxisId) {
            const nowList = cloneDeep(hardwareList)
            const parentData = getNodeById(nowList[nowTree].children, chanleNode?.parentAxisId)
            return parentData?.type
        }

        return false
    }, [chanleNode])

    const hwKey = useMemo(() => {
        if (!hardwareList?.length || (!nowTree && nowTree !== 0)) {
            return ''
        }
        const res = hardwareList?.[nowTree]?.hwKey || hardwareList?.[nowTree]?.hwName
        return res
    }, [hardwareList, nowTree])

    useEffect(() => {
        if (open) {
            getList()
        }
    }, [open])

    // 从redux里面拿硬件管理器
    useEffect(() => {
        if (hardwareData) {
            setHardwareDetail(hardwareData)
        }
    }, [hardwareData])

    // 初始化数据
    useEffect(() => { // 处理编辑数据
        if (open && hardwareDetail?.length) {
            const comHardware = []
            hardwareDetail.forEach(hardware => {
const forHardware = cloneDeep(hardware)
const childrens = hardware?.children // 当前组件的childrens
forHardware.children = treeData()
                for (const children in childrens) {
                    forHardware.children = forHardware.children.map(newChildren => {
                        const nowChildren = { ...newChildren, hwKey: hardware.hwKey || hardware.hwName }
                        if (newChildren?.type === children) { // 开始处理
                            if (childrens[children]) {
                                nowChildren.children = childrens[children].map(nowChild => {
                                    return {
                                        ...nowChild,
                                        name: nowChild.axisName || nowChild.name,
                                        id: (nowChild.axisName ? nowChild.axisId : nowChild.channelId) ?? nowChild?.deviceId,
                                        type: nowChild.axisName ? HARWARE_CONST_TYPES.AXIS : nowChild.type,
                                        parentType: nowChild.axisName ? nowChild.type : HARWARE_CONST_TYPES.AXIS,
                                        sensorName: nowChild.sensorName ?? [],
                                        children: nowChild.axisName ? nowChild.children?.map(exit => {
                                            return {
                                                ...exit,
                                                sensorName: exit.sensorName ?? [],
                                                id: exit.channelId
                                            }
        }) : null
                                    }
                                })
                    }
                        }
                        return nowChildren
                    })
                }
                comHardware.push(forHardware)
            })

            setModifiedHardwares([])
            setDelHardwares([])
            setHardwareList(comHardware)
        } else {
            setHardwareList([])
            setModifiedHardwares([])
            setDelHardwares([])
            setNowTree('')
        }
    }, [open, hardwareDetail])

    useEffect(() => { // 对选择的通道进行回显编辑
        if (selectRow) {
            if (chanleNode?.parentType) {
                setNowEditData(chanleNode)
                if (ParentTypes.indexOf(chanleNode?.type) !== -1 && chanleNode?.parentType !== HARWARE_CONST_TYPES.CCSS) {
                    setEditType(chanleNode?.type)
                } else {
                    setEditType(chanleNode?.parentType)
                }
            } else {
                setEditType('')
            }
        } else if (!selectRow && nowTree !== '') { // 对选择的硬件进行编辑
            setNowEditData(hardwareList[nowTree])
        }
    }, [chanleNode, selectRow])

    useEffect(() => {
        if (hardwareList.length && hardwareList[nowTree]) {
            const nowList = hardwareList
            getCount(nowList)
        }
    }, [hardwareList])

    const getList = async () => {
        try {
            initHardwareList()
        } catch (error) {
            console.log(error)
        }
    }

    const getCount = (nowList) => { // 获取一个最新数量
        let nowModifed = cloneDeep(modifiedHardwares)
        nowModifed = nowModifed.map(i => {
            const hw = nowList.filter(hw => hw.hwId === i.hwId)
            if (hw.length) {
                return hw[0]
            }
            return i
        })

        setModifiedHardwares(nowModifed)
        nowList.forEach((item, key) => {
            if (item.children?.length) {
                for (const rowItem of item.children) {
                    if (rowItem?.children) {
                        const AllData = rowItem.children.filter(i => i.deleteFlag === 0)
                        switch (rowItem.type) { // 判断是何种类型进行加一
                            case HARWARE_CONST_TYPES.HANDBOX:
                                nowList[key].handboxCount = AllData.length
                                break

                            case HARWARE_CONST_TYPES.DA:
                                nowList[key].daCount = AllData.length
                                break

                            case HARWARE_CONST_TYPES.AD:
                                nowList[key].adCount = AllData.length
                                break

                            case HARWARE_CONST_TYPES.SERVO:
                                nowList[key].servoAxisCount = AllData.length
                                break
                            case HARWARE_CONST_TYPES.HWDEVICE:
                                nowList[key].hwDeviceCount = AllData.length
                                break
                            case HARWARE_CONST_TYPES.TEMP:
                                nowList[key].tempAxisCount = AllData.length
                                break

                            case HARWARE_CONST_TYPES.CREEP:
                                nowList[key].creepAxisCount = AllData.length
                                break

                            case HARWARE_CONST_TYPES.INPUT:
                                nowList[key].inputCount = AllData.length
                                break

                            case HARWARE_CONST_TYPES.OUTPUT:
                                nowList[key].outputCount = AllData.length
                                break

                            default:
                                break
                        }
                    }
                }
            }
        })
        return nowList
    }

    const openTreeFunc = e => {
        setSelectRow('')
        if (e === nowTree) {
            setNowEditData({})
            setNowTree('')
            setEditType('')
        } else {
            setNowEditData({})
            setEditFlag(false)
            setNowTree(e)
        }
    }

    const openLogic = () => { // 打开逻辑硬件
        setLogicData(mergeNodes(...cloneDeep(hardwareList)))
        setShowLogic(true)
    }

    const closeLogicFunc = () => { // 关闭逻辑硬件
        setShowLogic(false)
    }

    /**
     * 每个层级的菜单权限
     * @param {*} key
     */
    const handelSetMenu = (key) => {
        const tempMenu = {
            add: false,
            copy: false,
            remove: false,
            affix: false
        }
        const nowList = cloneDeep(hardwareList)
        const res = getNodeById(nowList[nowTree].children, key)
        // 没有parentType，说明是第二级
        // 如果有=CCSS，说明是第二级，可以添加和固定
        if (!('parentType' in res) || res.parentType === HARWARE_CONST_TYPES.CCSS) {
            tempMenu.add = true
            tempMenu.affix = true
        } else {
            // ParentType只能删除，和复制
            if (ParentTypes.includes(res.type)) {
                tempMenu.remove = true
                tempMenu.copy = true
            }
            // AxisTypes可以全部权限
            if (AxisTypes.includes(res.parentType)) {
                tempMenu.add = true
                tempMenu.affix = true
                tempMenu.remove = true
                tempMenu.copy = true
            }
        }
        setMenu(tempMenu)
    }

    /**
     * @description: 右键菜单
     * 1. 第一层：不要任何编辑，
     */
    const handleContextMenu = (e, key, type, param) => {
        closeRightMenuFunc()
        if (key) {
            e.stopPropagation()
            e.nativeEvent.stopImmediatePropagation()
        }
        setCurrentData(param)
        setMenuType(type)
        if (type && type === MENU_TYPES.PARENT) {
            const nowData = hardwareList[key - 1].children
            const allType = []
            for (const item of nowData) {
                if (item.deleteFlag === 0) {
                    allType.push(item.type)
                }
            }
            setTreeShowList(allType)
            return
        }
        if (type && type === MENU_TYPES.CHILDREN) {
            handelSetMenu(key)
            setNowChildrenKey(key)
            setSelectRow(key)
        }

        // 获得点击的位置
        let { clientX, clientY } = e
        rightClickRef.current.style.display = 'block'
        // 文档显示区的宽度
        const screenW = window.innerWidth
        const screenH = window.innerHeight
        // 右键菜单的宽度
        const rightClickRefW = rightClickRef.current.offsetWidth
        const rightClickRefH = rightClickRef.current.offsetHeight

        // right为true，说明鼠标点击的位置到浏览器的右边界的宽度可以放下contextmenu。
        clientX = (screenW - clientX) > rightClickRefW ? clientX : clientX - rightClickRefW
        clientY = (screenH - clientY) > rightClickRefH ? clientY : clientY - rightClickRefH
        rightClickRef.current.style.top = `${clientY}px`
        rightClickRef.current.style.left = `${clientX}px`
    }

    const closeRightMenuFunc = () => { // 达成条件后关闭右键菜单（非多选框区域）
        if (rightClickRef.current) { // 多选时不要关闭
            rightClickRef.current.style.display = 'none'
            document.onclick = null
        }
    }

    const mergeNodes = (...trees) => { // 对tree进行合并
        const mergedTree = [] // 新的值
        trees.forEach(tree => {
            if (tree?.children) {
                tree.children.forEach(item => {
                    if (!mergedTree.length) {
                        mergedTree.push(item)
                    } else if (mergedTree.filter(i => i?.type === item?.type).length) { // 继续合并
                        mergedTree.map(row => {
                            if (row?.type === item?.type) {
                                if (row.children && item.children) {
                                    row.children = [...row?.children, ...item.children]
                                } else if (item.children) {
                                    row.children = item.children
                                }
                            }
                            return row
                        })
                    } else if (!mergedTree.filter(i => i?.type === item?.type).length) { // 当前数据内暂无该类型
                        mergedTree.push(item)
                    }
                })
            }
        })

        return mergedTree
    }

    const addModfied = (hw = hardwareList[nowTree]) => {
        const nowModifed = cloneDeep(modifiedHardwares)
        const modifiedState = modifiedHardwares?.findIndex(i => i.hwId === hw.hwId) // 判断一下删除的值是否已经被添加到修改列表内，如果是，删除对应数据
        if (modifiedState === -1) {
            nowModifed.push(cloneDeep(hw))
        } else {
            nowModifed.splice(modifiedState, 1, cloneDeep(hw))
        }
        setModifiedHardwares(nowModifed) // 记录下来被编辑的内容
    }

    // 新增子级
    const handleAddChildren = (param) => {
      // 校验是否是 物理硬件
        const harwareData = param.type === HARWARE_CONST_TYPES.HWDEVICE ? HARWARE_HW_DATA5 : HARWARE_EXIT_DATA5
        const name = getLabel(param.children?.map(m => m?.name), HardwareType[param.type])
        const id = crypto.randomUUID()
        let tempData = {
            ...harwareData,
            channelId: id,
            type: addChanleDetail.current?.channelType,
            name,
            parentCcssId: hardwareList[nowTree].hwId,
            parentAxisId: param.id,
            parentId: nowChildrenKey,
            parentType: param.type,
            id,
            hwKey
        }
        if (param.type === HARWARE_CONST_TYPES.HWDEVICE) {
            tempData = {
                ...harwareData,
                channelId: id,
                type: addChanleDetail.current?.channelType,
                name,
                parentType: param.type,
                id,
                deviceId: id,
                parentId: nowChildrenKey
            }
        }

        if (isAffixRef.current) {
            tempData = {
                ...tempData,
                ...copyDataRef.current,
                id,
                channelId: id,
                name
            }
            // 粘贴重新设置id
            if (tempData?.children && tempData?.children.length > 0) {
                tempData.children = tempData.children.map(m => {
                    const id = crypto.randomUUID()
                    return { ...m, id, channelId: id }
                })
            }
        }
        return tempData
    }

    // 无type类型新增
    const handleNotTypeAddChildren = (param) => {
        const name = getLabel(param.children?.map(m => m?.name), HardwareType[nowChildren.type])
        const id = crypto.randomUUID()
        let tempData = {
            ...HARWARE_AXIS_DATA5,
            id,
            name,
            parentId: nowChildrenKey,
            parentType: nowChildren?.type,
            hwKey
        }
        if (isAffixRef.current) {
            tempData = {
                ...tempData,
                ...copyDataRef.current,
                id,
                name
            }
            // 粘贴重新设置id
            if (tempData?.children && tempData?.children.length > 0) {
                tempData.children = tempData.children.map(m => {
                    const id2 = crypto.randomUUID()
                    return {
                        ...m,
                        id: id2,
                        channelId: id2,
                        parentAxisId: id
                    }
                })
            }
        }
        return tempData
    }

    /**
     * 所有添加，删除，都走这个。。。太乱了，需要优化
     * @param {*} data
     * @param {*} value
     * @param {*} type
     * @returns
     */
    const getChidlren = (data, value, type) => {
        const fn = (d) => {
            if (Array.isArray(d)) { // 判断是否是数组
                for (let i = 0; i < d.length; i++) {
                    const e = d[i]
                    if (e.id === value) { // 数据循环每个子项，并且判断子项下边是否有id值
                        if (type === HARDWARE_EDIT_TYPES.REMOVE_CHILDREN) {
                            e.deleteFlag = 1 // 返回的结果等于每一项
                        }
                        if (type === HARDWARE_EDIT_TYPES.ADD_CHILDREN) { // 添加子项，获取当前值的type进行校验，判断是添加轴类型还是通道类型
                            if (addChanleDetail.current?.channelType) { // 有type当前进行添加的内容为通道
                                if (e.children) {
                                    e?.children?.push(handleAddChildren(e))
                                } else {
                                    e.children = [handleAddChildren(e)]
                                }
                            } else if (e.children) { // 新建数据无type，执行轴类新建,当前tree节点有children
                                e?.children?.push(handleNotTypeAddChildren(e))
                            } else { // 新建数据无type，执行轴类新建,当前tree节点无children
                                e.children = [handleNotTypeAddChildren(e)]
                            }
                        }
                        break
                    } else if (e.children) {
                        fn(e.children) // 递归调用下边的子项
                    }
                }
            }
        }

        fn(data)
        return data // 返回处理后的data更新DOM
    }
    const menuClickFunc = (type) => {
        if (type) {
            isAffixRef.current = false
            const nowList = cloneDeep(hardwareList)
            if (type === HARDWARE_EDIT_TYPES.REMOVE_CHILDREN) { // 编辑硬件children
                // 在删除前，先收集被删除的硬件信息
                const collectDeletedHardware = (children, targetId) => {
                    const deletedItems = []
                    const traverse = (nodes) => {
                        for (const node of nodes) {
                            if (node.id === targetId) {
                                // 收集当前节点及其所有子节点
                                const collectNode = (n) => {
                                    if (n.hwId || n.channelId || n.axisId) {
                                        deletedItems.push({
                                            ...n,
                                            deleteFlag: 1
                                        })
                                    }
                                    if (n.children && n.children.length > 0) {
                                        n.children.forEach(collectNode)
                                    }
                                }
                                collectNode(node)
                                return
                            }
                            if (node.children && node.children.length > 0) {
                                traverse(node.children)
                            }
                        }
                    }
                    traverse(children)
                    return deletedItems
                }

                const deletedHardwares = collectDeletedHardware(nowList[nowTree].children, nowChildrenKey)
                if (deletedHardwares.length > 0) {
                    setDelHardwares(prev => {
                        const existingIds = new Set(prev.map(item => item.hwId || item.channelId || item.axisId))
                        const newItems = deletedHardwares.filter(item => {
                            const itemId = item.hwId || item.channelId || item.axisId
                            return itemId && !existingIds.has(itemId)
                        })
                        return [...prev, ...newItems]
                    })
                }

                nowList[nowTree].children = getChidlren(nowList[nowTree].children, nowChildrenKey, type)
                console.log(nowList)
                addModfied(nowList[nowTree])
                setHardwareList(nowList)
            }
            if (type === HARDWARE_EDIT_TYPES.ADD_CHILDREN) {
                addChildren()
            }
            if (type === HARDWARE_EDIT_TYPES.COPY) {
                copyDataRef.current = nowChildren
            }
            if (type === HARDWARE_EDIT_TYPES.AFFIX) {
                if (!copyDataRef.current) {
                    message.error(t('请先复制'))
                    return
                }
                if (hwKey !== nowChildren.hwKey) {
                    message.error(t('只能在相同硬件下粘贴'))
                    return
                }
                if (![copyDataRef.current.type, copyDataRef.current.parentType].includes(nowChildren.type)) {
                    message.error(t('类型不一致，无法粘贴'))
                    return
                }
                if (copyDataRef.current.parentType === nowChildren.parentType) {
                    message.error(t('不能在同一级粘贴'))
                    return
                }
                isAffixRef.current = true
                addChildren()
            }
        }
    }
    const addChildren = () => {
        if (AxisTypes.indexOf(nowChildren?.type) !== -1) { // 添加轴
            nodeComType.current = CHILDREN_TYPES.axis
            parentTypeFunc()
        } else if (nowChildren?.type === HARWARE_CONST_TYPES.AXIS) { // 轴下全部为添加ad
            nodeComType.current = CHILDREN_TYPES.chanle
            // setNodeType(HARWARE_CONST_TYPES.AD)
            parentTypeFunc({
                channelType: HARWARE_CONST_TYPES.AD
            })
        } else { // 轴下全部为添加当前类型通道
            nodeComType.current = CHILDREN_TYPES.chanle
            parentTypeFunc({
                channelType: nowChildren?.type
            })
        }
    }

    const parentTypeFunc = e => { // 处理选择数据进行回显
        addChanleDetail.current = e
        const nowList = cloneDeep(hardwareList)

        nowList[nowTree].children = getChidlren(nowList[nowTree].children, nowChildrenKey, HARDWARE_EDIT_TYPES.ADD_CHILDREN) // 添加新数据
        addModfied(nowList[nowTree])
        setHardwareList(nowList)
    }

    // 对通道类编辑按类型显示出可编辑内容
    const editNodeFunc = (nodeId) => {
        setEditFlag(false)
        if (!nodeId.length) {
            // setSelectRow('')
            // 用户复选需清空可编辑项
        } else {
            setSelectRow(nodeId[0])
        }
    }

    const editTreeValue = (name, val) => { // 处理组件编辑返回值，同步修改tree
        const nowList = cloneDeep(hardwareList)
        if (!selectRow && nowTree !== '') { // 对选择的硬件进行编辑
            for (const item in nowList[nowTree]) {
                if (item === name && nowList[nowTree][item] !== val) {
                    addModfied(nowList[nowTree])
                    nowList[nowTree][item] = val
                }
            }
            setHardwareList(nowList)
            return
        }
        const editNode = cloneDeep(chanleNode)
        for (const item in editNode) {
            if (item === name) {
                editNode[item] = val
            }
        }

        nowList[nowTree].children = editNodeById(nowList[nowTree]?.children, selectRow, editNode)

        addModfied(nowList[nowTree])

        setHardwareList(nowList)
    }

    // 使用防抖替换节流
    const editTreeThrottle = debounce((name, val) => {
        editTreeValue(name, val)
    }, 300)

    // 量纲与单位的修改
    const unitTypeChange = debounce((unitTypeVal, unitVal) => {
        const nowList = cloneDeep(hardwareList)
        if (!selectRow && nowTree !== '') { // 对选择的硬件进行编辑
            for (const item in nowList[nowTree]) {
                if (item === 'unitType' && nowList[nowTree][item] !== unitTypeVal) {
                    addModfied(nowList[nowTree])
                    nowList[nowTree][item] = unitTypeVal
                }
                if (item === 'unit' && nowList[nowTree][item] !== unitVal) {
                    addModfied(nowList[nowTree])
                    nowList[nowTree][item] = unitVal
                }
            }
            setHardwareList(nowList)
        }
        const editNode = cloneDeep(chanleNode)
        for (const item in editNode) {
            if (item === 'unitType') {
                editNode[item] = unitTypeVal
            }
            if (item === 'unit') {
                editNode[item] = unitVal
            }
        }

        nowList[nowTree].children = editNodeById(nowList[nowTree]?.children, selectRow, editNode)

        addModfied(nowList[nowTree])

        setHardwareList(nowList)
    }, 300)

    const preserveHardware = async () => { // 保存硬件设置
        const nowList = cloneDeep(modifiedHardwares)

        try {
            // 检查物理硬件下的子硬件号是否重复
            for (const hardware of nowList) {
                if (hardware.children && Array.isArray(hardware.children)) {
                    const hwDeviceTree = hardware.children.find(tree => tree.type === HARWARE_CONST_TYPES.HWDEVICE)
                    if (hwDeviceTree && hwDeviceTree.children && Array.isArray(hwDeviceTree.children)) {
                        const activeSubHardwares = hwDeviceTree.children.filter(child => child.deleteFlag !== 1)
                        const subIdMap = new Map()

                        for (const subHardware of activeSubHardwares) {
                            const { subId } = subHardware
                            console.log('subId', subId, subHardware)
                            if (subId === null || subId === undefined) {
                                message.error(t(`硬件 "${hardware.hwName || hardware.hwKey}" 下${subHardware?.name}的子硬件号不能为空`))
                                return
                            }
                            // if (subId)
                            if (subIdMap.has(subId)) {
                                message.error(t(`硬件 "${hardware.hwName || hardware.hwKey}" 下的子硬件号 "${subId}" 重复，请修改后再保存`))
                                return
                            }
                            subIdMap.set(subId, subHardware.name)
                        }
                    }
                }
            }
        } catch (error) {
            console.log('error', error)
        }

        const apiData = []
        getCount(nowList).forEach(hardware => {
            const newChildren = {}
            // 检查children属性是否存在且为数组，避免TypeError
            if (hardware.children && Array.isArray(hardware.children)) {
                hardware.children.forEach(tree => {
                    // 对数组类型数据进行处理，处理成接口需要的参数
                    if (AxisTypes.indexOf(tree?.type) !== -1) {
                        newChildren[tree?.type] = tree?.children
                            .filter(i => i.deleteFlag !== 1)
                            .map(i => {
                                const filteredChildren = i.children?.filter(child => child.deleteFlag !== 1) || []
                                return { // 转换
                                    ...i,
                                    parentId: hardware.hwId,
                                    axisId: i.id,
                                    axisName: i.name,
                                    deleteFlag: i.deleteFlag,
                                    type: tree.type, // 确保type字段存在
                                    children: filteredChildren,
                                    adSensorCount: filteredChildren.length || i.adSensorCount || 0
                                }
                            })
                    } else {
                        // 处理硬件下的通道
                        if (tree?.children && Array.isArray(tree.children)) {
                            newChildren[tree?.type] = tree.children
                                .filter(i => i.deleteFlag !== 1)
                                .map(i => {
                                    return { // 转换
                                        ...i,
                                        parentAxisId: hardware.hwId,
                                        parentId: hardware.hwId,
                                        hwKey: hardware.hwKey,
                                        parentType: HARWARE_CONST_TYPES.CCSS,
                                        channelId: i.id,
                                        type: i.type || tree.type // 确保type字段存在
                                    }
                                })
                        } else {
                            newChildren[tree?.type] = []
                        }
                    }
                })
            }
            apiData.push({
                ...hardware,
                children: newChildren
            })
        })

        const nowDel = []

        // 按硬件ID分组删除的节点
        const deletedByHardware = {}
        delHardwares?.forEach(deletedNode => {
            // 根据节点类型确定所属硬件ID
            let hardwareId = deletedNode.parentCcssId || deletedNode.hwId
            if (!hardwareId && deletedNode.parentAxisId) {
                // 对于通道节点，可能需要从parentAxisId推导硬件ID
                hardwareId = deletedNode.parentAxisId
            }

            if (hardwareId) {
                if (!deletedByHardware[hardwareId]) {
                    // 从hardwareList中找到对应的硬件对象，获取完整信息
                    const originalHardware = hardwareList.find(hw => hw.hwId === hardwareId)
                    if (originalHardware) {
                        deletedByHardware[hardwareId] = {
                            // 包含后端ccss-spec要求的所有必需字段
                            hwId: hardwareId,
                            hwName: originalHardware.hwName,
                            hwPort: originalHardware.hwPort,
                            editHwName: originalHardware.editHwName,
                            hwKey: originalHardware.hwKey,
                            type: originalHardware.type,
                            version: originalHardware.version,
                            describe: originalHardware.describe,
                            servoAxisCount: originalHardware.servoAxisCount || 0,
                            tempAxisCount: originalHardware.tempAxisCount || 0,
                            creepAxisCount: originalHardware.creepAxisCount || 0,
                            adCount: originalHardware.adCount || 0,
                            daCount: originalHardware.daCount || 0,
                            inputCount: originalHardware.inputCount || 0,
                            outputCount: originalHardware.outputCount || 0,
                            handboxCount: originalHardware.handboxCount || 0,
                            hwDeviceCount: originalHardware.hwDeviceCount || 0,
                            deleteFlag: 1,
                            children: {}
                        }
                    } else {
                        // 如果找不到原始硬件信息，使用基本结构
                        deletedByHardware[hardwareId] = {
                            hwId: hardwareId,
                            deleteFlag: 1,
                            children: {}
                        }
                    }
                }

                const nodeType = deletedNode.type
                if (!deletedByHardware[hardwareId].children[nodeType]) {
                    deletedByHardware[hardwareId].children[nodeType] = []
                }

                // 根据节点类型进行不同的处理
                if (AxisTypes.indexOf(nodeType) !== -1) {
                    // 轴类型节点
                    deletedByHardware[hardwareId].children[nodeType].push({
                        ...deletedNode,
                        parentId: hardwareId,
                        axisId: deletedNode.id || deletedNode.axisId,
                        axisName: deletedNode.name || deletedNode.axisName,
                        type: nodeType,
                        adSensorCount: deletedNode.adSensorCount || 0
                    })
                } else {
                    // 通道类型节点
                    deletedByHardware[hardwareId].children[nodeType].push({
                        ...deletedNode,
                        parentId: hardwareId,
                        channelId: deletedNode.id || deletedNode.channelId,
                        type: nodeType,
                        parentType: HARWARE_CONST_TYPES.CCSS
                    })
                }
            }
        })

        // 将分组后的数据转换为数组
        Object.values(deletedByHardware).forEach(hardware => {
            nowDel.push(hardware)
        })

        try {
            await addHardware({
                modifiedHardwares: apiData, // 编辑、新建的值
                delHardwares: nowDel // 删除的值
            })
            message.success(t('保存成功'))
            setModifiedHardwares([])
            setDelHardwares([])
            // setOpen(false)
        } catch (error) {
            console.log(error)
        }
    }
    const handleHardwareRefresh = async () => {
        await hardwareRefresh()
        getList()
    }

    useEffect(() => {
        if (!sessionStorage.getItem('initHwinfo')) {
            sessionStorage.setItem('initHwinfo', 'true')
            // 首次进入时，默认读取硬件配置
            handleHardwareRefresh()
        }
    }, [])
    return (
        <div>
            <RightMenu
                onClick={menuClickFunc}
                rightClickRef={rightClickRef}
                t={t}
                onClose={closeRightMenuFunc}
                menu={menu}
                menuType={menuType}
                treeShowList={treeShowList}
                currentData={currentData}
            />
            <VModal
                open={open}
                title={t('硬件管理器')}
                onOk={() => setOpen(false)}
                footer={false}
                onCancel={() => setOpen(false)}
            >
                {/* 节点数据类型选择 */}
                {/* <SelectNodeType
                    closeNodeModal={closeNodeModalFunc}
                    successSelect={parentTypeFunc}
                    nodeType={nodeType}
                    addNodaTitle={addNodaTitle}
                    nodeComType={nodeComType}
                    messageApi={messageApi}
                    open={isChanleOpen}
                    t={t}
                /> */}

                <ModalContext>
                    <Logic
                        closeLogic={closeLogicFunc}
                        logicData={logicData}
                        showLogic={showLogic}
                        t={t}
                    />
                    <VPage
                        title={t(`硬件总览（${hardwareList?.length}）`)}
                        operate={(
                            <Space>
                                {(!!modifiedHardwares.length || !!delHardwares.length) && (
                                    <VButton type="primary" onClick={preserveHardware}>
                                        {t('保存硬件配置')}
                                    </VButton>
                                )}
                                <VButton onClick={handleHardwareRefresh}>
                                    {t('读取硬件配置')}
                                </VButton>
                                <VButton onClick={openLogic}>
                                    {t('查看逻辑硬件')}
                                </VButton>

                                <VButton>
                                    {t('帮助')}
                                </VButton>
                            </Space>
                        )}
                    >
                        <div className="context">
                            {/* <div className="left" onContextMenu={e => handleContextMenu(e, null, MENU_TYPES.CONTEXT)}> */}
                            <div className="left">
                                {hardwareList.map((i, k) => (
                                    <TreeCom
                                        onContextMenu={handleContextMenu}
                                        key={i.hwId}
                                        openTree={() => openTreeFunc(k)}
                                        onSelectedKeys={editNodeFunc}
                                        selectedKeys={[selectRow]}
                                        hwName={i.hwName}
                                        treeData={i.children}
                                        nowTree={nowTree === k}
                                        t={t}
                                        comId={k + 1}
                                        param={i}
                                    />
                                ))}
                            </div>

                            <div className="right">
                                {editType === HARWARE_CONST_TYPES.AD && selectRow
                                    && (
                                    <AdCom
                                        parentType={adParentType}
                                        backValue={editTreeThrottle}
                                        unitTypeChange={unitTypeChange}
                                        t={t}
                                        nowEditData={nowEditData}
                                    />
)}
                                {editType === HARWARE_CONST_TYPES.DA && selectRow
                                    && (
                                    <DaCom
                                        backValue={editTreeThrottle}
                                        t={t}
                                        nowEditData={nowEditData}
                                    />
)}
                                {editType === HARWARE_CONST_TYPES.OUTPUT && selectRow
                                    && (
                                    <OutCom
                                        backValue={editTreeThrottle}
                                        t={t}
                                        nowEditData={nowEditData}
                                    />
)}
                                {editType === HARWARE_CONST_TYPES.INPUT && selectRow
                                    && (
                                    <InCom
                                        backValue={editTreeThrottle}
                                        t={t}
                                        nowEditData={nowEditData}
                                    />
)}
                                {editType === HARWARE_CONST_TYPES.HWDEVICE && selectRow
                                    && (
                                    <HwDevice
                                        backValue={editTreeThrottle}
                                        t={t}
                                        nowEditData={nowEditData}
                                    />
)}
                                {!selectRow && nowTree !== '' && (
                                    <HardFormCom
                                        editFlag={editFlag}
                                        setEditFlag={setEditFlag}
                                        backValue={editTreeThrottle}
                                        t={t}
                                        nowEditData={nowEditData}
                                    />
                                )}
                                {/* {editType === 'AD' && <TempCom t={t} />} */}
                                {(AxisTypes.indexOf(editType) !== -1) && <ServoCom showType={nowEditData?.parentType} backValue={editTreeThrottle} t={t} nowEditData={nowEditData} />}
                            </div>
                        </div>
                    </VPage>
                </ModalContext>
            </VModal>
        </div>

    )
}

export default HardwareModal
