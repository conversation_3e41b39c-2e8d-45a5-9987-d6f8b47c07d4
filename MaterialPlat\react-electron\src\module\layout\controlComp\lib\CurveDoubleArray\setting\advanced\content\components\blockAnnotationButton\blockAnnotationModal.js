import React, {
    useMemo, useState, useEffect, useRef
} from 'react'
import {
    Select, Form, Row, Col, Checkbox, Tree, List, Button, Input, Switch
} from 'antd'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import cloneDeep from 'lodash/cloneDeep'
import { v4 as uuidv4 } from 'uuid'
import useResult from '@/hooks/useResult'
import ColorSelector from '@/components/colorSelector'
import VButton from '@/components/vButton'
import VTransfer from '@/components/vTransfer'
import VModal from '@/components/vModal'

import { BlockAnnotationModalContainer } from './style'

const BLOCK_INIT_DATA = {
    id: '',
    color: '#000000',
    isName: true,
    name: '',
    isChunk: true,
    isLine: true,
    isSample: true,
    isVal: true,
    isAbbr: true,
    curveIndex: 0,
    results: []
}

const { Item } = Form

const BlockAnnotationModal = ({
    open, setOpen, value = [], onChange
}) => {
    const { t } = useTranslation()

    const resultData = useSelector(state => state.template.resultData)
    const resultTestData = useSelector(state => state.template.resultTestData)
    const { getCurveResults } = useResult()

    const [form] = Form.useForm()
    const [isAll, setIsAll] = useState(false)

    const data = useRef()
    const [blockList, setBlockList] = useState([])
    const [selectedBlockIndex, setSelectedBlockIndex] = useState(null)
    const [selectedBlock, setSelectedBlock] = useState(null)

    // 曲线上要显示的结果变量
    const resultsVarsList = useMemo(() => getCurveResults(), [resultData, resultTestData])

    useEffect(() => {
        if (open) {
            data.current = cloneDeep(value)
            setBlockList(cloneDeep(value))
            setSelectedBlockIndex(null)
            setSelectedBlock(null)
            form.resetFields()
        }
    }, [open, value])

    const handleModalClose = () => {
        setOpen(false)
    }

    const handleClickConfirm = () => {
        onChange(data.current)
        setOpen(false)
    }

    // 新增块
    const handleAddBlock = () => {
        const newBlock = {
            ...BLOCK_INIT_DATA,
            id: uuidv4(),
            showTitle: true,
            title: `块${blockList.length + 1}`
        }

        const updatedList = [...blockList, newBlock]
        setBlockList(updatedList)
        data.current = updatedList

        // 自动选中新添加的块
        setSelectedBlockIndex(updatedList.length - 1)
        setSelectedBlock(newBlock)
        form.setFieldsValue(newBlock)
    }

    // 删除块
    const handleDeleteBlock = (index) => {
        const updatedList = blockList.filter((_, i) => i !== index)
        setBlockList(updatedList)
        data.current = updatedList

        // 如果删除的是当前选中的块，重置选择状态
        if (selectedBlockIndex === index) {
            setSelectedBlockIndex(null)
            setSelectedBlock(null)
            form.resetFields()
        } else if (selectedBlockIndex > index) {
            // 如果删除的块在当前选中块之前，需要调整选中索引
            setSelectedBlockIndex(selectedBlockIndex - 1)
        }
    }

    // 选择块
    const handleSelectBlock = (index) => {
        setSelectedBlockIndex(index)
        const block = blockList[index]
        setSelectedBlock(block)
        form.setFieldsValue(block)
    }

    // 处理表单值变化
    const handleFormValuesChange = (changedValues, allValues) => {
        if (selectedBlockIndex !== null) {
            const updatedList = [...blockList]
            updatedList[selectedBlockIndex] = {
                ...updatedList[selectedBlockIndex],
                ...allValues
            }

            // 如果勾选了"全部"，将当前更改的配置应用到所有块
            if (isAll && Object.keys(changedValues).length > 0) {
                // 获取要同步的字段（排除isAll、id、name、results、title等唯一字段）
                const fieldsToSync = Object.keys(changedValues).filter(field => !['id', 'name', 'results', 'title'].includes(field))

                if (fieldsToSync.length > 0) {
                    // 只同步已更改的字段
                    const configToSync = {}
                    fieldsToSync.forEach(field => {
                        configToSync[field] = allValues[field]
                    })

                    // 应用到所有块，但保持每个块的唯一字段
                    for (let i = 0; i < updatedList.length; i += 1) {
                        if (i !== selectedBlockIndex) {
                            updatedList[i] = {
                                ...updatedList[i],
                                ...configToSync
                            }
                        }
                    }
                }
            }

            setBlockList(updatedList)
            setSelectedBlock(updatedList[selectedBlockIndex])
            data.current = updatedList
        }
    }

    // 处理结果变量选择
    const handleResultChange = (keys) => {
        if (selectedBlockIndex !== null) {
            const updatedList = [...blockList]
            updatedList[selectedBlockIndex] = {
                ...updatedList[selectedBlockIndex],
                results: keys
            }
            setBlockList(updatedList)
            setSelectedBlock(updatedList[selectedBlockIndex])
            data.current = updatedList

            // 更新表单中的results字段
            form.setFieldsValue({
                results: keys
            })
        }
    }

    const handleResultChangeWay = (result) => {
        // 当选择结果变量时的处理逻辑
        console.log('Selected result:', result)
    }

    const handleResultChangeDelWay = (result) => {
        // 当删除结果变量时的处理逻辑
        if (selectedBlockIndex !== null && result?.result_variable_id) {
            const updatedList = [...blockList]
            const currentResults = updatedList[selectedBlockIndex].results || []

            // 从results数组中移除指定的result_variable_id
            const newResults = currentResults.filter(id => id !== result.result_variable_id)

            updatedList[selectedBlockIndex] = {
                ...updatedList[selectedBlockIndex],
                results: newResults
            }

            setBlockList(updatedList)
            setSelectedBlock(updatedList[selectedBlockIndex])
            data.current = updatedList

            // 更新表单中的results字段
            form.setFieldsValue({
                results: newResults
            })
        }
    }

    return (
        <VModal
            open={open}
            onCancel={handleModalClose}
            width={1200}
            title={t('块标注设置')}
            destroyOnClose
            footer={null}
        >
            <BlockAnnotationModalContainer>
                <Row gutter={16}>
                    {/* 左侧块列表 */}
                    <Col span={8}>
                        <div className="block-list-container">
                            <div className="block-list-header">
                                <span>{t('块列表')}</span>
                                <Button
                                    icon={<PlusOutlined />}
                                    size="small"
                                    onClick={handleAddBlock}
                                >
                                    {t('新增')}
                                </Button>
                            </div>
                            <List
                                className="block-list"
                                dataSource={blockList}
                                renderItem={(item, index) => (
                                    <List.Item
                                        className={`block-item ${selectedBlockIndex === index ? 'selected' : ''}`}
                                        onClick={() => handleSelectBlock(index)}
                                        actions={[
                                            <Button
                                                type="text"
                                                danger
                                                icon={<DeleteOutlined />}
                                                size="small"
                                                onClick={(e) => {
                                                    e.stopPropagation()
                                                    handleDeleteBlock(index)
                                                }}
                                            />
                                        ]}
                                    >
                                        <div className="block-item-content">
                                            <div
                                                className="block-color"
                                                style={{ backgroundColor: item.color }}
                                            />
                                            <span className="block-name">{item.title}</span>
                                        </div>
                                    </List.Item>
                                )}
                            />
                        </div>
                    </Col>

                    {/* 右侧配置区域 */}
                    <Col span={16}>
                        <div className="block-config-container">
                            {selectedBlock ? (
                                <>
                                    {/* 配置表单 */}
                                    <Form
                                        form={form}
                                        // layout="vertical"
                                        onValuesChange={handleFormValuesChange}
                                        className="block-config-form"
                                    >
                                        <Row>
                                            <Col span={6}>
                                                <Item
                                                    name={['showTitle']}
                                                    label={t('块名称')}
                                                    valuePropName="checked"
                                                    labelCol={{ span: 12 }}
                                                >
                                                    <Checkbox />
                                                </Item>
                                            </Col>
                                            <Col span={6} pull={2}>
                                                <Item
                                                    name="title"
                                                >
                                                    <Input placeholder={t('请输入块名称')} />
                                                </Item>
                                            </Col>

                                            <Col span={12}>
                                                <div style={{ display: 'flex', alignItems: 'center', gap: '3px' }}>
                                                    <Item>
                                                        <Switch value={isAll} onChange={v => setIsAll(v)} />
                                                    </Item>
                                                    <Item label={t('开启时的后续修改会同步到所有块标注')} colon={false} />
                                                </div>
                                            </Col>
                                        </Row>

                                        <div className="checkbox-group">
                                            <Row gutter={16}>
                                                <Col span={6}>
                                                    <Item
                                                        name="isAbbr"
                                                        valuePropName="checked"
                                                    >
                                                        <Checkbox>{t('缩写')}</Checkbox>
                                                    </Item>
                                                </Col>
                                                <Col span={6}>
                                                    <Item
                                                        name="isName"
                                                        valuePropName="checked"
                                                    >
                                                        <Checkbox>{t('名称')}</Checkbox>
                                                    </Item>
                                                </Col>
                                                <Col span={6}>
                                                    <Item
                                                        name="isVal"
                                                        valuePropName="checked"
                                                    >
                                                        <Checkbox>{t('数值')}</Checkbox>
                                                    </Item>
                                                </Col>
                                                <Col span={4}>
                                                    <Item
                                                        name="color"
                                                        label={t('颜色')}
                                                    >
                                                        <ColorSelector />
                                                    </Item>
                                                </Col>
                                            </Row>
                                        </div>
                                        <div className="transfer-container">
                                            <VTransfer
                                                listStyle={{
                                                    width: '20vw',
                                                    height: '30vh'
                                                }}
                                                targetKeys={selectedBlock.results || []}
                                                onChange={handleResultChange}
                                                dataSource={resultsVarsList}
                                                onChangeWay={handleResultChangeWay}
                                                onChangeDelWay={handleResultChangeDelWay}
                                                oneWay
                                                oneWayLabel="variable_name"
                                                rowKey="result_variable_id"
                                                render={(item) => item.variable_name}
                                            />
                                        </div>
                                    </Form>
                                </>
                            ) : (
                                <div className="no-selection">
                                    <p>{t('请选择一个块进行配置')}</p>
                                </div>
                            )}
                        </div>
                    </Col>
                </Row>

                <div className="footer-btns">
                    <VButton
                        onClick={handleClickConfirm}
                        type="primary"
                    >
                        {t('确认')}
                    </VButton>
                    <VButton
                        onClick={handleModalClose}
                    >
                        {t('取消')}
                    </VButton>
                </div>
            </BlockAnnotationModalContainer>
        </VModal>
    )
}

export default BlockAnnotationModal
