2025-09-09 09:39:04,064 [qtp1355421519-247] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:52)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:37)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:25)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invokeStatic(service.clj:536)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invoke(service.clj:522)
	at clj_backend.modules.sys.user.sys_user_service$login.invokeStatic(sys_user_service.clj:96)
	at clj_backend.modules.sys.user.sys_user_service$login.invoke(sys_user_service.clj:91)
	at clj_backend.modules.sys.user.sys_user_routes$routes$fn__43086.invoke(sys_user_routes.clj:56)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
