- [新消息](#新消息)
  - [topic](#topic)
  - [消息内容](#消息内容)
- [订阅接口](#订阅接口)
  - [参数](#参数)
- [订阅使用场景](#订阅使用场景)
  - [DAQ](#daq)
    - [表头/特殊表头](#表头特殊表头)
    - [daq曲线](#daq曲线)
      - [变化](#变化)
      - [非试验](#非试验)
      - [试验中](#试验中)
  - [二维数组](#二维数组)
    - [二维数组表格](#二维数组表格)
      - [变化](#变化-1)
      - [编辑表格](#编辑表格)
      - [只读表格](#只读表格)
    - [蠕变分屏监控表格](#蠕变分屏监控表格)
    - [二维数组/二维数组集合 曲线](#二维数组二维数组集合-曲线)
      - [变化](#变化-2)
      - [非试验](#非试验-1)
      - [试验中](#试验中-1)

## 新消息

### topic

```js
`${getProcessID()}-ControlCompUIData-${controlCompId}-UIData`
```

### 消息内容

```json
{
    "mode": 0, // 更新模式：0 - 全量更控件之前数据丢弃    1 - 增量 控件之前数据保留
    "sampleCode": "sample_14785d372", // 试样code  为了daq曲线区分 数据来源
    "doubleArrayIndex": 0, // 二维数组对应集合中的下标  为了二维数组集合区分   数据来源
    "controlCompId": "c2e-1-1-T1753239736952-2-T1753239736952", // 控件id - 考虑放到topic中 避免无用消耗
    "totalCount": 3, // 当前数据总量 - 主要用在二维数组表格页码显示
    "data": { // 每个通道的数据
        "time": [ // 时间通道数据
            1306.9,
            1306.9001,
            1306.9002
        ],
        "index": [ // 数据点的下标
            0,
            1,
            2
        ]
    }
}
```

## 订阅接口

### 参数

```json
{
  "templateName": "", // 项目id
  "controlCompId": "", // 控件id
  "dataSourceType": "", //  数据源类型: 1. daqbuffer 2. doubleArray 3. doubleArraySet
  "dataSourceCode": "", // 变量内部名
  "dataCodes": ["time","weiyi"], // 订阅的通道code
  "timer": 20, // 频率：毫秒数     -1 则等待脚本触发推送  比如输入编辑类的二维数组表格 
  "number": "", // 订阅的条数 n|-1  -1则不指定 场景 1. 表头：固定最新1条  2. 二维数组表格 最新n条 3. 曲线 -1 
  "testStatus": 0, // 当前试验状态  是否获取历史数据推送上来 0 非试验 | 1 试验中
  "daqCurveSelectedSampleCodes"：[] // daq曲线 非试验状态使用  c#：非试验状态 daqbuffer 解析该字段 
}
```

## 订阅使用场景

不同场景下的特殊参数处理 主要是下面三个

1. timer
2. number
3. testStatus

### DAQ

#### 表头/特殊表头

特殊表头和表头一样，只是显示方式不一样

**要求**
1. 只需要最新的一条数据显示
2. 不区分试验状态，默认试验中开始

**订阅参数**
```json
{
  "timer": 100, // 更新频率
  "number": 1, // 指定只需要一条数据
  "testStatus": 1, // 试验状态默认试验中
}
```

#### daq曲线

##### 变化
1. 试验状态
   1. testStatus
      1. 试验中：1
      2. 非试验：0
   2. timer
      1. 试验中：用户设置的更新频率
      2. 非试验：-1
2. 当前试样 / 多选试样 / 全部试样
   1. daqCurveSelectedSampleCodes 试样code 

##### 非试验

daq曲线非试验状态下需要传递daqCurveSelectedSampleCodes参数

**订阅参数**
```json
{
  "timer": -1, // -1 推送一次数据后不再推送
  "number": -1, // 不指定数量
  "testStatus": 0, // 非试验
  "daqCurveSelectedSampleCodes": ["sample_14785d372"] // 选中试样code
}
```

##### 试验中

1. daq曲线现在都是增量，抽点啥的现在用的二维数组曲线全量更

**订阅参数**
```json
{
  "timer": 200, // 指定更新频率
  "number": -1, // 不指定数量
  "testStatus": 1, // 非试验
}
```

### 二维数组

#### 二维数组表格

##### 变化
1. 可编辑
   1. timer
      1. 可编辑：-1
      2. 只读：用户设置的更新频率
   2. number
      1. 可编辑： -1
      2. 只读：用户设置的显示数量
2. 当前试样
   1. daqCurveSelectedSampleCodes 选中的试样code

##### 编辑表格

**要求**
1. 编辑表格需要脚本控制消息推送

**订阅参数**
```json
{
  "timer": -1, // -1 等待脚本触发推送
  "number": -1, // -1 不指定数量
  "testStatus": 1, // 默认试验中
}
```

##### 只读表格

**要求**
1. 现在订阅只会订阅指定数量数据
2. 历史数据根据页码或者滚动位置调接口获取

**订阅参数**
```json
{
  "timer": 200, // 用户设置的更新频率
  "number": 20, // 用户设置的显示数量
  "testStatus": 1, // 默认试验中
}
```

#### 蠕变分屏监控表格

1. 需要订阅全部数据变化


**订阅参数**
```json
{
  "timer": 200, // 用户设置的更新频率
  "number": -1, // 用户设置的显示数量
  "testStatus": 1, // 默认试验中
}
```

#### 二维数组/二维数组集合 曲线

##### 变化
1. 试验状态
   1. testStatus
      1. 试验中：1
      2. 非试验：0
   2. timer
      1. 试验中：用户设置的更新频率
      2. 非试验：-1
2. 当前试样
   1. daqCurveSelectedSampleCodes 选中的试样code

##### 非试验

非试验只获取当前的历史数据

**订阅参数**
```json
{
  "timer": -1, // 不更新
  "number": -1, // 不指定数量
  "testStatus": 0, // 非试验
  "daqCurveSelectedSampleCodes": ["sample_14785d372"] // 选中试样code
}
```

##### 试验中


这个时候需要后端推送数据时 判断当前数据源变量脚本设置的更新模式是全量还是增量

1. 全量 - 则需要后端推送全部数据
2. 增量 - 则只需要推送最新的数据


**订阅参数**
```json
{
  "timer": 200, // 指定更新频率
  "number": -1, // 不指定数量
  "testStatus": 1, // 试验中
  "daqCurveSelectedSampleCodes": ["sample_14785d372"] // 选中试样code
}
```
